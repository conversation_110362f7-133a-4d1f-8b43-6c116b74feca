import request from '@/utils/request'

// 查询外部接口调用日志列表
export function listExternalApiLog(query) {
  return request({
    url: '/system/apilog/list',
    method: 'get',
    params: query
  })
}

// 查询外部接口调用日志详细
export function getExternalApiLog(logId) {
  return request({
    url: '/system/apilog/' + logId,
    method: 'get'
  })
}

// 删除外部接口调用日志
export function delExternalApiLog(logId) {
  return request({
    url: '/system/apilog/' + logId,
    method: 'delete'
  })
}

// 清空外部接口调用日志
export function cleanExternalApiLog() {
  return request({
    url: '/system/apilog/clean',
    method: 'delete'
  })
}
