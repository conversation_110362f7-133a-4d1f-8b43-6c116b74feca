<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          maxlength="20"
          show-word-limit
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="描述" prop="templateDescribe">
        <el-input
          v-model="queryParams.templateDescribe"
          placeholder="请输入描述"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['device:template:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['device:template:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-document-copy"
          size="mini"
          :disabled="single"
          @click="handleCopy"
          v-hasPermi="['device:template:add']"
        >复制</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['device:template:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="table-container" style="overflow-x: auto;">
      <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange" style="width: 100%; min-width: 1000px;" :header-cell-style="{background:'#eef1f6',color:'#606266'}" border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模板ID" align="center" prop="templateId" width="80" />
      <el-table-column label="模板名称" align="center" prop="templateName" :show-overflow-tooltip="true" min-width="120" />
      <el-table-column label="星期" align="center" prop="templateWeek" width="100">
        <template slot-scope="scope">
          <span>{{ formatWeek(scope.row.templateWeek) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="描述" align="center" prop="templateDescribe" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="总号数" align="center" prop="totalPlan" width="80">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold; font-size: 14px;">{{ scope.row.totalPlan }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['device:template:edit']"
          >修改</el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['device:template:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    </div>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改排班模板对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="模板名称" prop="templateName">
              <el-input
                v-model="form.templateName"
                placeholder="请输入模板名称"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="星期" prop="templateWeek">
              <el-select v-model="form.templateWeek" placeholder="请选择星期" style="width: 100%">
                <el-option
                  v-for="item in weekOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="templateDescribe">
          <el-input
            v-model="form.templateDescribe"
            type="textarea"
            :rows="2"
            placeholder="请输入模板描述"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>

        <!-- 时间段列表 -->
        <el-divider content-position="center">时间段设置</el-divider>

        <div v-for="(timeSlot, index) in form.templateInfoList" :key="index" class="time-slot-container">
          <el-card class="time-slot-card">
            <div slot="header" class="clearfix">
              <span>时间段 {{ index + 1 }}</span>
              <span style="margin-left: 15px;">
                班次：
                <el-select
                  v-model="timeSlot.planCount"
                  placeholder="请选择班次"
                  size="mini"
                  style="width: 100px;"
                >
                  <el-option
                    v-for="dict in dict.type.plan_count"
                    :key="dict.value"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  />
                </el-select>
              </span>
              <el-button
                style="float: right; padding: 3px 0"
                type="text"
                icon="el-icon-delete"
                @click="removeTimeSlot(index)"
              >删除</el-button>
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="'开始时间'" :prop="'templateInfoList.' + index + '.stime'" :rules="[
                  { required: true, message: '开始时间不能为空', trigger: 'change' }
                ]">
                  <el-time-select
                    v-model="timeSlot.stime"
                    placeholder="选择开始时间"
                    :picker-options="{
                      start: '00:00',
                      end: '23:45',
                      step: '00:15'
                    }"
                    style="width: 100%">
                  </el-time-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="'结束时间'" :prop="'templateInfoList.' + index + '.etime'" :rules="[
                  { required: true, message: '结束时间不能为空', trigger: 'change' },
                  { validator: validateEndTime, trigger: 'change' }
                ]">
                  <el-time-select
                    v-model="timeSlot.etime"
                    placeholder="选择结束时间"
                    :picker-options="{
                      start: '00:00',
                      end: '23:45',
                      step: '00:15',
                      minTime: timeSlot.stime
                    }"
                    style="width: 100%">
                  </el-time-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="'普通号'" :prop="'templateInfoList.' + index + '.ordinaryPlan'" :rules="[
                  { required: true, message: '普通号不能为空', trigger: 'blur' }
                ]">
                  <div style="display: flex; align-items: center;">
                    <el-input-number v-model="timeSlot.ordinaryPlan" :min="0" :max="100" @change="calculateTimeSlotTotal(index)" />

                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="'急诊号'" :prop="'templateInfoList.' + index + '.emergencyPlan'" :rules="[
                  { required: true, message: '急诊号不能为空', trigger: 'blur' }
                ]">
                  <div style="display: flex; align-items: center;">
                    <el-input-number v-model="timeSlot.emergencyPlan" :min="0" :max="100" @change="calculateTimeSlotTotal(index)" />

                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="'预留号'" :prop="'templateInfoList.' + index + '.reservePlan'" :rules="[
                  { required: true, message: '预留号不能为空', trigger: 'blur' }
                ]">
                  <div style="display: flex; align-items: center;">
                    <el-input-number v-model="timeSlot.reservePlan" :min="0" :max="100" @change="calculateTimeSlotTotal(index)" />

                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <div class="time-slot-total">
                  <span>时间段总号数：</span>
                  <span style="color: #E6A23C; font-weight: bold;">{{ timeSlot.reservePlan + timeSlot.emergencyPlan + timeSlot.ordinaryPlan }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </div>

        <div class="add-time-slot">
          <el-button type="primary" icon="el-icon-plus" @click="addTimeSlot">添加时间段</el-button>
        </div>

        <el-divider content-position="center">总计</el-divider>

        <el-form-item label="总号数" prop="totalPlan">
          <div style="display: flex; align-items: center;">
            <el-input-number v-model="form.totalPlan" :min="0" :max="1000" disabled />
            <span style="margin-left: 10px; color: #E6A23C; font-weight: bold;">总号数</span>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTemplate, getTemplate, delTemplate, addTemplate, updateTemplate } from "@/api/device/template";

export default {
  name: "Template",
  dicts: ['plan_count'],
  data() {
    // 结束时间验证函数
    const validateEndTime = (rule, value, callback) => {
      // 从字段名中提取索引，例如 'templateInfoList.0.etime'
      const index = parseInt(rule.field.split('.')[1]);
      const timeSlot = this.form.templateInfoList[index];
      if (timeSlot.stime && value && value <= timeSlot.stime) {
        callback(new Error('结束时间必须大于开始时间'));
      } else {
        callback();
      }
    };

    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 排班模板表格数据
      templateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 结束时间验证函数
      validateEndTime,

      // 星期选项
      weekOptions: [
        { value: "星期一", label: "星期一" },
        { value: "星期二", label: "星期二" },
        { value: "星期三", label: "星期三" },
        { value: "星期四", label: "星期四" },
        { value: "星期五", label: "星期五" },
        { value: "星期六", label: "星期六" },
        { value: "星期日", label: "星期日" }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: undefined,
        templateDescribe: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        templateName: [
          { required: true, message: "模板名称不能为空", trigger: "blur" },
          { max: 20, message: "模板名称长度不能超过20个字符", trigger: "blur" }
        ],
        templateWeek: [
          { required: true, message: "星期不能为空", trigger: "change" }
        ],
        totalPlan: [
          { validator: (_, value, callback) => {
            if (value <= 0) {
              callback(new Error('总号数必须大于0'));
            } else {
              callback();
            }
          }, trigger: 'change' }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询排班模板列表 */
    getList() {
      console.log('getList - 开始获取模板列表');
      this.loading = true;
      listTemplate(this.queryParams).then(response => {
        console.log('getList - 获取模板列表成功:', response);
        this.templateList = response.rows;
        this.total = response.total;
        this.loading = false;

        // 打印模板列表信息，方便调试
        if (this.templateList && this.templateList.length > 0) {
          console.log('getList - 模板列表长度:', this.templateList.length);
          this.templateList.forEach((template, index) => {
            console.log(`getList - 模板[${index}]:`, {
              templateId: template.templateId,
              templateName: template.templateName,
              week: template.week,
              stime: template.stime,
              etime: template.etime
            });
          });
        } else {
          console.log('getList - 模板列表为空');
        }
      }).catch(error => {
        console.error('getList - 获取模板列表失败:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        templateId: undefined,
        templateName: undefined,
        templateDescribe: undefined,
        templateWeek: undefined,
        totalPlan: 0,
        templateInfoList: [this.createEmptyTimeSlot()]
      };

      this.resetForm("form");
    },

    // 创建空的时间段对象
    createEmptyTimeSlot() {
      return {
        templateInfoId: undefined,
        templateId: undefined,
        stime: undefined,
        etime: undefined,
        ordinaryPlan: 0,
        emergencyPlan: 0,
        reservePlan: 0,
        planCount: null, // 设置为 null，以便用户可以选择班次
        isDel: 0
      };
    },

    // 添加时间段
    addTimeSlot() {
      if (!this.form.templateInfoList) {
        this.form.templateInfoList = [];
      }
      const newTimeSlot = this.createEmptyTimeSlot();
      // 如果是修改模式，设置templateId
      if (this.form.templateId) {
        newTimeSlot.templateId = this.form.templateId;
      }
      this.form.templateInfoList.push(newTimeSlot);
      console.log('addTimeSlot - 添加新时间段:', newTimeSlot);
    },

    // 移除时间段
    removeTimeSlot(index) {
      this.form.templateInfoList.splice(index, 1);
      if (this.form.templateInfoList.length === 0) {
        this.addTimeSlot();
      }
      this.calculateTotal();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.templateId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      console.log('handleAdd - 添加排班模板');
      this.reset();

      // 确保模板列表已经加载完成
      if (!this.templateList || this.templateList.length === 0) {
        console.log('handleAdd - 模板列表为空，重新加载');
        this.getList();
      } else {
        console.log('handleAdd - 模板列表已加载，长度:', this.templateList.length);
      }

      this.open = true;
      this.title = "添加排班模板";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      console.log('handleUpdate - 修改排班模板');
      this.reset();
      const templateId = row.templateId || this.ids;

      // 确保模板列表已经加载完成
      if (!this.templateList || this.templateList.length === 0) {
        console.log('handleUpdate - 模板列表为空，重新加载');
        this.getList();
      } else {
        console.log('handleUpdate - 模板列表已加载，长度:', this.templateList.length);
      }

      console.log(`handleUpdate - 获取模板详情，ID: ${templateId}`);
      getTemplate(templateId).then(response => {
        console.log('handleUpdate - 获取模板详情成功:', response.data);
        this.form = response.data;

        this.open = true;
        this.title = "修改排班模板";
      }).catch(error => {
        console.error('handleUpdate - 获取模板详情失败:', error);
      });
    },

    /** 复制按钮操作 */
    handleCopy(row) {
      console.log('handleCopy - 复制排班模板');
      this.reset();
      const templateId = row.templateId || this.ids;

      // 确保模板列表已经加载完成
      if (!this.templateList || this.templateList.length === 0) {
        console.log('handleCopy - 模板列表为空，重新加载');
        this.getList();
      } else {
        console.log('handleCopy - 模板列表已加载，长度:', this.templateList.length);
      }

      console.log(`handleCopy - 获取模板详情，ID: ${templateId}`);
      getTemplate(templateId).then(response => {
        console.log('handleCopy - 获取模板详情成功:', response.data);
        // 复制模板数据，但移除ID字段，以便创建新记录
        const templateData = response.data;
        this.form = { ...templateData };
        // 移除ID字段，修改模板名称
        delete this.form.templateId;
        this.form.templateName = this.form.templateName + "_复制";

        // 清除时间段的ID字段，以便创建新记录
        if (this.form.templateInfoList && this.form.templateInfoList.length > 0) {
          this.form.templateInfoList.forEach(timeSlot => {
            delete timeSlot.templateInfoId;
            delete timeSlot.templateId;
            console.log('handleCopy - 清除时间段ID字段:', timeSlot);
          });
        }

        this.open = true;
        this.title = "复制排班模板";
      }).catch(error => {
        console.error('handleCopy - 获取模板详情失败:', error);
      });
    },
    /** 检查时间区间是否重叠 */
    checkTimeOverlap() {
      const { templateWeek, templateId, templateInfoList } = this.form;

      console.log('checkTimeOverlap - 当前模板信息:', { templateWeek, templateId });
      console.log('checkTimeOverlap - 模板列表长度:', this.templateList.length);

      // 如果缺少必要的字段，不进行检查
      if (!templateWeek || !templateInfoList || templateInfoList.length === 0) {
        console.log('checkTimeOverlap - 缺少必要字段，跳过检查');
        return Promise.resolve(true);
      }

      // 转换时间字符串为分钟数，方便比较
      const convertTimeToMinutes = (timeStr) => {
        if (!timeStr || typeof timeStr !== 'string') {
          console.error('convertTimeToMinutes - 无效的时间字符串:', timeStr);
          return 0;
        }
        try {
          const parts = timeStr.split(':');
          if (parts.length !== 2) {
            console.error('convertTimeToMinutes - 时间格式不正确:', timeStr);
            return 0;
          }
          const hours = parseInt(parts[0], 10);
          const minutes = parseInt(parts[1], 10);
          if (isNaN(hours) || isNaN(minutes)) {
            console.error('convertTimeToMinutes - 时间转换失败:', timeStr, hours, minutes);
            return 0;
          }
          return hours * 60 + minutes;
        } catch (error) {
          console.error('convertTimeToMinutes - 异常:', error, timeStr);
          return 0;
        }
      };

      // 首先检查当前模板内部的时间段是否有重叠
      for (let i = 0; i < templateInfoList.length; i++) {
        const timeSlot1 = templateInfoList[i];
        if (!timeSlot1.stime || !timeSlot1.etime) continue;

        const start1 = convertTimeToMinutes(timeSlot1.stime);
        const end1 = convertTimeToMinutes(timeSlot1.etime);

        for (let j = i + 1; j < templateInfoList.length; j++) {
          const timeSlot2 = templateInfoList[j];
          if (!timeSlot2.stime || !timeSlot2.etime) continue;

          const start2 = convertTimeToMinutes(timeSlot2.stime);
          const end2 = convertTimeToMinutes(timeSlot2.etime);

          // 检查时间区间是否重叠
          const case1 = start1 >= start2 && start1 < end2;
          const case2 = end1 > start2 && end1 <= end2;
          const case3 = start1 <= start2 && end1 >= end2;

          if (case1 || case2 || case3) {
            console.log(`checkTimeOverlap - 发现内部时间段重叠: ${timeSlot1.stime}-${timeSlot1.etime} 与 ${timeSlot2.stime}-${timeSlot2.etime}`);
            this.$message.error(`时间段 ${timeSlot1.stime}-${timeSlot1.etime} 与 ${timeSlot2.stime}-${timeSlot2.etime} 重叠，请重新设置时间段`);
            return Promise.reject(new Error(`时间区间重叠`));
          }
        }
      }

      // 检查是否与其他模板的时间段重叠
      try {
        if (!this.templateList || !Array.isArray(this.templateList)) {
          console.error('checkTimeOverlap - 模板列表无效:', this.templateList);
          return Promise.resolve(true); // 如果模板列表无效，则跳过检查
        }

        console.log(`checkTimeOverlap - 开始检查 ${this.templateList.length} 个模板是否重叠`);

        for (const template of this.templateList) {
          try {
            // 如果模板对象无效，跳过该模板
            if (!template || typeof template !== 'object') {
              console.error('checkTimeOverlap - 无效的模板对象:', template);
              continue;
            }

            // 如果是编辑模式，忽略当前模板
            if (templateId && template.templateId === templateId) {
              console.log(`checkTimeOverlap - 忽略当前模板 ID: ${template.templateId}`);
              continue;
            }

            // 如果星期不同，不重叠
            if (template.templateWeek !== templateWeek) {
              console.log(`checkTimeOverlap - 星期不同，不重叠: ${template.templateWeek} !== ${templateWeek}`);
              continue;
            }

            console.log(`checkTimeOverlap - 检查模板: ${template.templateName}`);

            // 检查每个时间段是否与其他模板的时间段重叠
            if (!template.templateInfoList || template.templateInfoList.length === 0) {
              console.log(`checkTimeOverlap - 模板 ${template.templateName} 没有时间段信息`);
              continue;
            }

            for (const currentTimeSlot of templateInfoList) {
              if (!currentTimeSlot.stime || !currentTimeSlot.etime) continue;

              const currentStartMinutes = convertTimeToMinutes(currentTimeSlot.stime);
              const currentEndMinutes = convertTimeToMinutes(currentTimeSlot.etime);

              for (const templateTimeSlot of template.templateInfoList) {
                if (!templateTimeSlot.stime || !templateTimeSlot.etime) continue;

                const templateStartMinutes = convertTimeToMinutes(templateTimeSlot.stime);
                const templateEndMinutes = convertTimeToMinutes(templateTimeSlot.etime);

                console.log(`checkTimeOverlap - 比较时间段: ${currentTimeSlot.stime}-${currentTimeSlot.etime} 与 ${templateTimeSlot.stime}-${templateTimeSlot.etime}`);

                // 检查时间区间是否重叠
                const case1 = currentStartMinutes >= templateStartMinutes && currentStartMinutes < templateEndMinutes;
                const case2 = currentEndMinutes > templateStartMinutes && currentEndMinutes <= templateEndMinutes;
                const case3 = currentStartMinutes <= templateStartMinutes && currentEndMinutes >= templateEndMinutes;

                const isOverlapping = case1 || case2 || case3;

                if (isOverlapping) {
                  console.log(`checkTimeOverlap - 发现重叠模板: ${template.templateName}, 时间段: ${templateTimeSlot.stime}-${templateTimeSlot.etime}`);
                  this.$message.error(`时间段 ${currentTimeSlot.stime}-${currentTimeSlot.etime} 与模板 "${template.templateName}" 的时间段 ${templateTimeSlot.stime}-${templateTimeSlot.etime} 重叠，请重新设置时间段`);
                  return Promise.reject(new Error(`时间区间重叠`));
                }
              }
            }
          } catch (error) {
            console.error('checkTimeOverlap - 检查模板时发生异常:', error, template);
            continue; // 如果发生异常，跳过该模板
          }
        }

        console.log('checkTimeOverlap - 没有发现重叠模板');
        return Promise.resolve(true);
      } catch (error) {
        console.error('checkTimeOverlap - 检查时间区间重叠时发生异常:', error);
        return Promise.resolve(true); // 如果发生异常，则跳过检查
      }
    },

    /** 提交按钮 */
    submitForm() {
      console.log('submitForm - 开始提交表单');
      this.$refs["form"].validate(valid => {
        console.log('submitForm - 表单验证结果:', valid);
        if (valid) {
          // 首先检查时间区间是否重叠
          console.log('submitForm - 开始检查时间区间重叠');
          this.checkTimeOverlap().then(() => {
            console.log('submitForm - 时间区间检查通过，准备提交数据');

            // 在提交前确保所有时间段都有正确的templateId（仅在修改模式下）
            if (this.form.templateId != null && this.form.templateInfoList) {
              this.form.templateInfoList.forEach(timeSlot => {
                if (!timeSlot.templateId) {
                  timeSlot.templateId = this.form.templateId;
                  console.log('submitForm - 为时间段设置templateId:', timeSlot);
                }
              });
            }

            if (this.form.templateId != null) {
              console.log('submitForm - 执行修改操作');
              updateTemplate(this.form).then(() => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.error('submitForm - 修改操作失败:', error);
              });
            } else {
              console.log('submitForm - 执行新增操作');
              addTemplate(this.form).then(() => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.error('submitForm - 新增操作失败:', error);
              });
            }
          }).catch(error => {
            // 时间重叠检查失败，错误消息已在 checkTimeOverlap 方法中显示
            console.error('submitForm - 时间重叠检查失败:', error);
          });
        } else {
          console.log('submitForm - 表单验证失败，不提交数据');
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const templateIds = row.templateId || this.ids;
      this.$modal.confirm('是否确认删除排班模板编号为"' + templateIds + '"的数据项？').then(function() {
        return delTemplate(templateIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 计算时间段总计划数 */
    calculateTimeSlotTotal(index) {
      const timeSlot = this.form.templateInfoList[index];
      if (timeSlot) {
        // 更新总计划数
        this.calculateTotal();
      }
    },

    /** 计算总计划数 */
    calculateTotal() {
      if (!this.form.templateInfoList || this.form.templateInfoList.length === 0) {
        this.form.totalPlan = 0;
        return;
      }

      let total = 0;
      this.form.templateInfoList.forEach(timeSlot => {
        total += timeSlot.ordinaryPlan + timeSlot.emergencyPlan + timeSlot.reservePlan;
      });

      this.form.totalPlan = total;
    },
    /** 格式化星期显示 */
    formatWeek(templateWeek) {
      return templateWeek || "";
    }
  }
};
</script>

<style scoped>
.time-slot-container {
  margin-bottom: 20px;
}

.time-slot-card {
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.time-slot-total {
  margin-top: 10px;
  text-align: right;
  font-size: 14px;
  font-weight: bold;
}

.add-time-slot {
  margin: 20px 0;
  text-align: center;
}

.time-slot-item {
  margin-bottom: 5px;
  padding: 5px;
  border-bottom: 1px dashed #ebeef5;
}

.time-slot-item:last-child {
  border-bottom: none;
}

.plan-numbers {
  margin-top: 3px;
  font-size: 12px;
}

.ordinary-plan {
  color: #67C23A;
  font-weight: bold;
}

.emergency-plan {
  color: #F56C6C;
  font-weight: bold;
}

.reserve-plan {
  color: #409EFF;
  font-weight: bold;
}
</style>
