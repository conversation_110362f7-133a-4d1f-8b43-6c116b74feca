import request from '@/utils/request'

// 查询设备规则列表
export function listRule(query) {
  return request({
    url: '/device/rule/list',
    method: 'get',
    params: query
  })
}

// 查询设备规则详细
export function getRule(ruleId) {
  return request({
    url: '/device/rule/' + ruleId,
    method: 'get'
  })
}

// 新增设备规则
export function addRule(data) {
  return request({
    url: '/device/rule',
    method: 'post',
    data: data
  })
}

// 修改设备规则
export function updateRule(data) {
  return request({
    url: '/device/rule',
    method: 'put',
    data: data
  })
}

// 删除设备规则
export function delRule(ruleId) {
  return request({
    url: '/device/rule/' + ruleId,
    method: 'delete'
  })
}

// 获取设备的规则列表
export function getDeviceRules(deviceId) {
  return request({
    url: '/device/rule/device/' + deviceId,
    method: 'get'
  })
}

// 为设备分配规则
export function assignRules(deviceId, ruleIds) {
  return request({
    url: '/device/rule/assign/' + deviceId,
    method: 'post',
    data: ruleIds
  })
}

// 移除设备规则
export function removeDeviceRule(deviceId, ruleId) {
  return request({
    url: '/device/rule/device/' + deviceId + '/rule/' + ruleId,
    method: 'delete'
  })
}
