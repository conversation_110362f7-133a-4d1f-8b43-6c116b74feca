<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 规则列表 -->
      <el-col :span="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
          label-width="68px">
          <el-form-item label="规则名称" prop="ruleName">
            <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="规则状态" clearable style="width: 240px">
              <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['device:rule:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              v-hasPermi="['device:rule:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['device:rule:remove']">删除</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="ruleList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="规则ID" align="center" prop="ruleId" />
          <el-table-column label="规则名称" align="center" prop="ruleName" :show-overflow-tooltip="true" />
          <el-table-column label="规则描述" align="center" prop="ruleDesc" :show-overflow-tooltip="true" />
          <el-table-column label="规则数量" align="center" prop="ruleSize" />
          <el-table-column label="状态" align="center" prop="status">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                @change="handleStatusChange(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['device:rule:edit']">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['device:rule:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改设备规则对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则描述" prop="ruleDesc">
          <el-input v-model="form.ruleDesc" type="textarea" placeholder="请输入规则描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-divider content-position="center">规则条件</el-divider>



        <div v-for="(condition, index) in form.conditions" :key="index" class="condition-item">
          <el-row :gutter="10">
            <el-col :span="10">
              <el-form-item :label="'条件类型'" :prop="'conditions.' + index + '.conditionType'"
                :rules="{ required: true, message: '条件类型不能为空', trigger: 'change' }">
                <el-select v-model="condition.conditionType" placeholder="请选择条件类型" style="width: 100%"
                  @change="handleConditionTypeChange(index)">
                  <el-option v-for="dict in dict.type.rule_condition_type" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item :label="'操作符'" :prop="'conditions.' + index + '.conditionOperator'"
                :rules="{ required: true, message: '操作符不能为空', trigger: 'change' }">
                <el-select v-model="condition.conditionOperator" placeholder="请选择操作符" style="width: 100%"
                  @change="handleOperatorChange(index)">
                  <el-option label="等于" value="=" v-if="condition.conditionType !== 'time'" />
                  <el-option label="不等于" value="!=" v-if="condition.conditionType !== 'time'" />
                  <el-option label="包含于" value="in" v-if="condition.conditionType !== 'time'" />
                  <el-option label="不包含于" value="not_in" v-if="condition.conditionType !== 'time'" />
                  <el-option label="区间" value="between" v-if="condition.conditionType === 'time'" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" style="display: flex; align-items: center; justify-content: center;">
              <el-button type="danger" icon="el-icon-delete" circle @click="removeCondition(index)"></el-button>
            </el-col>
          </el-row>

          <!-- 条件值放到第二行 -->
          <el-row :gutter="10" style="margin-top: 10px;">
            <el-col :span="24">
              <el-form-item :label="'条件值'" :prop="'conditions.' + index + '.conditionValue'"
                :rules="{ required: true, message: '条件值不能为空', trigger: 'blur' }">
                <!-- 时间段条件 -->
                <div v-if="condition.conditionType === 'time' && condition.conditionOperator === 'between'">
                  <el-time-picker v-model="condition.startTime" format="HH:mm:ss" placeholder="开始时间" style="width: 48%"
                    @change="updateTimeRangeFromModel(index)" value-format="HH:mm:ss" clearable></el-time-picker>
                  <span style="margin: 0 4px;">至</span>
                  <el-time-picker v-model="condition.endTime" format="HH:mm:ss" placeholder="结束时间" style="width: 48%"
                    @change="updateTimeRangeFromModel(index)" value-format="HH:mm:ss" clearable></el-time-picker>
                </div>

                <!-- 部门条件 -->
                <div v-else-if="condition.conditionType === 'dept'" class="condition-value-container">
                  <div class="tag-container">
                    <el-tag v-for="tag in getConditionTags(condition.conditionValue, index)" :key="tag.value" closable
                      @close="removeConditionTag(index, tag.value)" style="margin-right: 5px; margin-bottom: 5px; max-width: 100%;">
                      <span style="white-space: normal; word-break: break-all;">{{ tag.label }}</span>
                    </el-tag>
                  </div>
                  <el-button size="small" type="primary" plain @click="openDeptSelector(index)">
                    选择
                  </el-button>
                </div>
                <!-- 检查部位条件 -->
                <div v-else-if="condition.conditionType === 'body_part'" class="condition-value-container">
                  <div class="tag-container">
                    <el-tag v-for="tag in getConditionTags(condition.conditionValue, index)" :key="tag.value" closable
                      @close="removeConditionTag(index, tag.value)" style="margin-right: 5px; margin-bottom: 5px; max-width: 100%;">
                      <span style="white-space: normal; word-break: break-all;">{{ tag.label }}</span>
                    </el-tag>
                  </div>
                  <el-button size="small" type="primary" plain @click="openBodyPartSelector(index)">
                    选择
                  </el-button>
                </div>
                <!-- 患者来源条件 -->
                <div v-else-if="condition.conditionType === 'patient_source'" class="condition-value-container">
                  <div class="tag-container">
                    <el-tag v-for="tag in getConditionTags(condition.conditionValue, index)" :key="tag.value" closable
                      @close="removeConditionTag(index, tag.value)" style="margin-right: 5px; margin-bottom: 5px; max-width: 100%;">
                      <span style="white-space: normal; word-break: break-all;">{{ tag.label }}</span>
                    </el-tag>
                  </div>
                  <el-button size="small" type="primary" plain @click="openPatientSourceSelector(index)">
                    选择
                  </el-button>
                </div>

                <!-- 其他条件 -->
                <div v-else-if="condition.conditionType !== 'time'" class="condition-value-container">
                  <!-- 标签显示区域 -->
                  <div class="tag-container">
                    <el-tag v-for="tag in getCustomTags(condition.conditionValue)" :key="tag.value" closable
                      @close="removeCustomTag(index, tag.value)" style="margin-right: 5px; margin-bottom: 5px; max-width: 100%;">
                      <span style="white-space: normal; word-break: break-all;">{{ tag.label }}</span>
                    </el-tag>
                  </div>
                  <!-- 选择按钮 -->
                  <el-button size="small" type="primary" plain @click="openCustomSelector(index)">
                    选择
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider v-if="index < form.conditions.length - 1"></el-divider>
        </div>

        <div v-if="form.conditions.length === 0" style="text-align: center; color: #909399; margin: 20px 0;">
          请添加规则条件
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="addCondition">添加规则</el-button>
        <el-button type="primary" size="mini" @click="submitForm">确 定</el-button>
        <el-button size="mini" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 检查部位选择器对话框 -->
    <el-dialog title="选择检查部位" :visible.sync="bodyPartDialogVisible" width="500px" append-to-body>
      <el-table ref="bodyPartTable" :data="bodyPartOptions" height="300px"
        @selection-change="handleBodyPartSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="value" label="值" width="120"></el-table-column>
        <el-table-column prop="label" label="名称"></el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleBodyPartSelect">确 定</el-button>
        <el-button @click="bodyPartDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 患者来源选择器对话框 -->
    <el-dialog title="选择患者来源" :visible.sync="patientSourceDialogVisible" width="500px" append-to-body>
      <el-table ref="patientSourceTable" :data="patientSourceOptions" height="300px"
        @selection-change="handlePatientSourceSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="value" label="值" width="120"></el-table-column>
        <el-table-column prop="label" label="名称"></el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handlePatientSourceSelect">确 定</el-button>
        <el-button @click="patientSourceDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 自定义选择器对话框 -->
    <el-dialog title="选择条件值" :visible.sync="customDialogVisible" width="500px" append-to-body>
      <el-input v-model="customSearchValue" placeholder="请输入搜索关键词" clearable style="margin-bottom: 15px;">
        <el-button slot="append" icon="el-icon-search" @click="searchCustomValues"></el-button>
      </el-input>

      <el-table ref="customTable" :data="customOptions" height="300px" @selection-change="handleCustomSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="value" label="值" width="120"></el-table-column>
        <el-table-column prop="label" label="名称"></el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleCustomSelect">确 定</el-button>
        <el-button @click="customDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRule, getRule, addRule, updateRule, delRule } from "@/api/device/rule";
import { listDept } from "@/api/system/dept";
import { listData } from "@/api/system/dict/data";

export default {
  name: "DeviceRule",
  dicts: ['rule_condition_type', 'study_body_part', 'study_body_part_code', 'study_patient_source'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备规则表格数据
      ruleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态选项
      statusOptions: [
        { value: "0", label: "正常" },
        { value: "1", label: "停用" }
      ],
      // 注意：时间值现在直接存储在条件对象中
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: null,
        status: null
      },
      // 表单参数
      form: {
        ruleId: null,
        ruleName: null,
        ruleDesc: null,
        status: "0",
        conditions: []
      },
      // 表单校验
      rules: {
        ruleName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "规则状态不能为空", trigger: "change" }
        ]
      },
      // 部门选择对话框可见性
      deptDialogVisible: false,
      // 部门树选项
      deptOptions: [],
      // 当前编辑的条件索引
      currentConditionIndex: -1,
      // 选中的部门键
      selectedDeptKeys: [],
      // 部门标签映射
      deptLabelMap: {},

      // 检查部位选择对话框可见性
      bodyPartDialogVisible: false,
      // 检查部位选项
      bodyPartOptions: [],
      // 选中的检查部位
      selectedBodyParts: [],

      // 患者来源选择对话框可见性
      patientSourceDialogVisible: false,
      // 患者来源选项
      patientSourceOptions: [],
      // 选中的患者来源
      selectedPatientSources: [],

      // 自定义选择器对话框可见性
      customDialogVisible: false,
      // 自定义选项
      customOptions: [],
      // 原始自定义选项
      originalCustomOptions: [],
      // 选中的自定义值
      selectedCustomValues: [],
      // 搜索值
      customSearchValue: ''
    };
  },
  created() {
    this.getList();
    this.getDeptTree();

    // 初始化选项
    this.initBodyPartOptions();
    this.initPatientSourceOptions();

    // 延迟再次初始化，确保字典数据已加载
    setTimeout(() => {
      this.initBodyPartOptions();
      this.initPatientSourceOptions();
    }, 1000);
  },
  methods: {
    /** 查询设备规则列表 */
    getList() {
      this.loading = true;
      listRule(this.queryParams).then(response => {
        this.ruleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        ruleId: null,
        ruleName: null,
        ruleDesc: null,
        status: "0",
        conditions: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.ruleId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const ruleId = row.ruleId || this.ids;
      getRule(ruleId).then(response => {
        this.form = response.data;
        // 初始化时间选择器的值
        if (this.form.conditions && this.form.conditions.length > 0) {
          this.form.conditions.forEach((condition, index) => {
            if (condition.conditionType === 'time') {
              if (condition.conditionOperator === 'between') {
                const timeValues = condition.conditionValue.split(',');
                // 设置开始时间和结束时间
                this.$set(condition, 'startTime', timeValues[0] || '');
                this.$set(condition, 'endTime', timeValues[1] || '');
              } else if (condition.conditionOperator === '=') {
                // 设置单个时间
                this.$set(condition, 'singleTime', condition.conditionValue || '');
              }
            }
          });
        }
        this.open = true;
        this.title = "修改设备规则";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.ruleId != null) {
            updateRule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ruleIds = row.ruleId || this.ids;
      this.$modal.confirm('是否确认删除规则编号为"' + ruleIds + '"的数据项？').then(function () {
        return delRule(ruleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.ruleName + '"规则吗？').then(function () {
        return updateRule(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    /** 添加条件 */
    addCondition() {
      this.form.conditions.push({
        conditionType: '',
        conditionOperator: '',
        conditionValue: ''
      });
    },
    /** 移除条件 */
    removeCondition(index) {
      this.form.conditions.splice(index, 1);
    },
    /** 获取开始时间 */
    getStartTime(index) {
      console.log("this.form.conditions[index].conditionValue:" + this.form.conditions[index].conditionValue)
      if (this.form.conditions[index].conditionValue) {
        const timeValues = this.form.conditions[index].conditionValue.split(',');
        // 如果开始时间为空，返回null，允许时间选择器显示为空
        return timeValues[0] || null;
      }
      return null;
    },

    /** 获取结束时间 */
    getEndTime(index) {
      if (this.form.conditions[index].conditionValue) {
        const timeValues = this.form.conditions[index].conditionValue.split(',');
        // 如果结束时间为空，返回null，允许时间选择器显示为空
        return timeValues[1] || null;
      }
      return null;
    },

    /** 获取单个时间 */
    getSingleTime(index) {
      // 如果条件值为空，返回null，允许时间选择器显示为空
      return this.form.conditions[index].conditionValue || null;
    },

    /** 从模型更新时间范围 */
    updateTimeRangeFromModel(index) {
      const condition = this.form.conditions[index];
      const startTime = condition.startTime || '';
      const endTime = condition.endTime || '';

      // 更新条件值
      this.$set(condition, 'conditionValue', startTime + ',' + endTime);
      console.log('Updated time range from model:', condition.conditionValue);
    },

    /** 从模型更新单个时间 */
    updateSingleTimeFromModel(index) {
      const condition = this.form.conditions[index];

      // 更新条件值
      this.$set(condition, 'conditionValue', condition.singleTime || '');
      console.log('Updated single time from model:', condition.conditionValue);
    },

    /** 处理条件类型变化 */
    handleConditionTypeChange(index) {
      // 重置操作符和条件值
      this.form.conditions[index].conditionOperator = '';
      this.form.conditions[index].conditionValue = '';

      // 如果是时间类型，初始化时间选择器
      // 注意：具体的时间值将在选择操作符时初始化
    },

    /** 处理操作符变化 */
    handleOperatorChange(index) {
      // 重置条件值
      this.form.conditions[index].conditionValue = '';

      // 如果是时间类型且操作符是区间
      if (this.form.conditions[index].conditionType === 'time' && this.form.conditions[index].conditionOperator === 'between') {
        // 初始化时间区间值为空，允许用户选择
        this.$set(this.form.conditions[index], 'conditionValue', ',');
        // 初始化开始时间和结束时间
        this.$set(this.form.conditions[index], 'startTime', '');
        this.$set(this.form.conditions[index], 'endTime', '');
      } else if (this.form.conditions[index].conditionType === 'time' && this.form.conditions[index].conditionOperator === '=') {
        // 初始化单个时间值为空，允许用户选择
        this.$set(this.form.conditions[index], 'conditionValue', '');
        // 初始化单个时间
        this.$set(this.form.conditions[index], 'singleTime', '');
      }
    },

    /** 获取部门树 */
    getDeptTree() {
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, "deptId", "parentId");
        // 构建部门ID和名称的映射
        this.buildDeptLabelMap(this.deptOptions);
      });
    },

    /** 构建部门标签映射 */
    buildDeptLabelMap(depts) {
      depts.forEach(dept => {
        this.deptLabelMap[dept.id] = dept.label;
        if (dept.children && dept.children.length > 0) {
          this.buildDeptLabelMap(dept.children);
        }
      });
    },

    /** 初始化检查部位选项 */
    initBodyPartOptions() {
      console.log("Initializing body part options");

      // 使用默认数据作为备用
      const defaultOptions = [
        { value: 'HEAD', label: '头部' },
        { value: 'CHEST', label: '胸部' },
        { value: 'ABDOMEN', label: '腹部' },
        { value: 'PELVIS', label: '盆腔' },
        { value: 'SPINE', label: '脊柱' },
        { value: 'EXTREMITY', label: '四肢' }
      ];

      // 先使用默认数据，确保始终有数据显示
      this.bodyPartOptions = [...defaultOptions];

      // 尝试从字典中获取数据
      if (this.dict.type.study_body_part_code && this.dict.type.study_body_part_code.length > 0) {
        console.log("Using study_body_part_code dictionary", this.dict.type.study_body_part_code);
        this.bodyPartOptions = this.dict.type.study_body_part_code.map(item => {
          return { value: item.dictValue, label: item.dictLabel };
        });
      } else if (this.dict.type.study_body_part && this.dict.type.study_body_part.length > 0) {
        console.log("Using study_body_part dictionary", this.dict.type.study_body_part);
        this.bodyPartOptions = this.dict.type.study_body_part.map(item => {
          return { value: item.dictValue, label: item.dictLabel };
        });
      } else {
        console.log("No dictionary data found, using default data");
      }

      // 如果字典为空，尝试从后端获取数据
      this.getDictDataByType('study_body_part_code').then(response => {
        console.log("API response for study_body_part_code:", response);

        if (response && response.rows && response.rows.length > 0) {
          console.log("Got study_body_part_code data from API:", response.rows);
          this.bodyPartOptions = response.rows.map(item => {
            return { value: item.dictValue, label: item.dictLabel };
          });
          console.log("Updated body part options from API:", this.bodyPartOptions);
        } else if (response && response.data && response.data.length > 0) {
          console.log("Got study_body_part_code data from API (data field):", response.data);
          this.bodyPartOptions = response.data.map(item => {
            return { value: item.dictValue, label: item.dictLabel };
          });
          console.log("Updated body part options from API (data field):", this.bodyPartOptions);
        } else {
          console.log("No data from API, keeping current options");
        }
      }).catch(error => {
        console.error("Error getting dictionary data:", error);
        console.log("Keeping default body part options due to API error");
      });
    },

    /** 从后端获取字典数据 */
    getDictDataByType(dictType) {
      console.log("Fetching dictionary data for type:", dictType);
      return listData({
        dictType: dictType,
        pageSize: 100  // 确保获取足够的数据
      });
    },

    /** 初始化患者来源选项 */
    initPatientSourceOptions() {
      console.log("Initializing patient source options");

      // 使用默认数据作为备用
      const defaultOptions = [
        { value: '门诊', label: '门诊' },
        { value: '住院', label: '住院' },
        { value: '急诊', label: '急诊' },
        { value: '体检', label: '体检' }
      ];

      // 先使用默认数据，确保始终有数据显示
      this.patientSourceOptions = [...defaultOptions];

      // 尝试从字典中获取数据
      if (this.dict.type.study_patient_source && this.dict.type.study_patient_source.length > 0) {
        console.log("Using study_patient_source dictionary", this.dict.type.study_patient_source);
        this.patientSourceOptions = this.dict.type.study_patient_source.map(item => {
          return { value: item.dictValue, label: item.dictLabel };
        });
      } else {
        console.log("No dictionary data found, using default data");
      }

      // 如果字典为空，尝试从后端获取数据
      this.getDictDataByType('study_patient_source').then(response => {
        console.log("API response for study_patient_source:", response);

        if (response && response.rows && response.rows.length > 0) {
          console.log("Got study_patient_source data from API:", response.rows);
          this.patientSourceOptions = response.rows.map(item => {
            return { value: item.dictValue, label: item.dictLabel };
          });
          console.log("Updated patient source options from API:", this.patientSourceOptions);
        } else if (response && response.data && response.data.length > 0) {
          console.log("Got study_patient_source data from API (data field):", response.data);
          this.patientSourceOptions = response.data.map(item => {
            return { value: item.dictValue, label: item.dictLabel };
          });
          console.log("Updated patient source options from API (data field):", this.patientSourceOptions);
        } else {
          console.log("No data from API, keeping current options");
        }
      }).catch(error => {
        console.error("Error getting dictionary data:", error);
        console.log("Keeping default patient source options due to API error");
      });
    },

    /** 打开部门选择器 */
    openDeptSelector(index) {
      this.currentConditionIndex = index;
      this.deptDialogVisible = true;

      // 如果已有选中的部门，预选中
      const conditionValue = this.form.conditions[index].conditionValue;
      if (conditionValue) {
        this.selectedDeptKeys = conditionValue.split(',');
      } else {
        this.selectedDeptKeys = [];
      }
    },

    /** 打开检查部位选择器 */
    openBodyPartSelector(index) {
      // 记录当前编辑的条件索引
      this.currentConditionIndex = index;

      // 获取已选中的检查部位
      const conditionValue = this.form.conditions[index].conditionValue;
      let selectedBodyParts = conditionValue ? conditionValue.split(',').filter(v => v !== '') : [];


      // 显示对话框
      this.bodyPartDialogVisible = true;

      // 使用 nextTick 确保对话框已经渲染
      this.$nextTick(() => {
       console.log("进入：")
          // 清除现有选中状态
          this.$refs.bodyPartTable.clearSelection();

          // 强制选中特定行
          this.bodyPartOptions.forEach(row => {
            if (selectedBodyParts.includes(row.value)) {
              this.$refs.bodyPartTable.toggleRowSelection(row, true);
            }
          });

      });
    },

    /** 打开患者来源选择器 */
    openPatientSourceSelector(index) {
      // 记录当前编辑的条件索引
      this.currentConditionIndex = index;

      // 获取已选中的患者来源
      const conditionValue = this.form.conditions[index].conditionValue;
      let selectedPatientSources = conditionValue ? conditionValue.split(',').filter(v => v !== '') : [];

      // 显示对话框
      this.patientSourceDialogVisible = true;

      // 使用 nextTick 确保对话框已经渲染
      this.$nextTick(() => {

        // 清除现有选中状态
        this.$refs.patientSourceTable.clearSelection();

        // 选中行
        this.patientSourceOptions.forEach(row => {
          if (selectedPatientSources.includes(row.value)) {
            this.$refs.patientSourceTable.toggleRowSelection(row, true);
          }
        });
      });
    },

    /** 处理部门选择 */
    handleDeptSelect() {
      const checkedKeys = this.$refs.deptTree.getCheckedKeys();
      if (checkedKeys.length === 0) {
        this.$message.warning('请至少选择一个部门');
        return;
      }

      // 更新条件值
      this.form.conditions[this.currentConditionIndex].conditionValue = checkedKeys.join(',');
      this.deptDialogVisible = false;
    },

    /** 处理检查部位选择框选中变化 */
    handleBodyPartSelectionChange(selection) {
      this.selectedBodyParts = selection.map(item => item.value);
    },

    /** 处理检查部位选择 */
    handleBodyPartSelect() {
      if (this.selectedBodyParts.length === 0) {
        this.$message.warning('请至少选择一个检查部位');
        return;
      }

      // 更新条件值
      this.form.conditions[this.currentConditionIndex].conditionValue = this.selectedBodyParts.join(',');
      this.bodyPartDialogVisible = false;
    },

    /** 处理患者来源选择框选中变化 */
    handlePatientSourceSelectionChange(selection) {
      this.selectedPatientSources = selection.map(item => item.value);
    },

    /** 处理患者来源选择 */
    handlePatientSourceSelect() {
      if (this.selectedPatientSources.length === 0) {
        this.$message.warning('请至少选择一个患者来源');
        return;
      }

      // 更新条件值
      this.form.conditions[this.currentConditionIndex].conditionValue = this.selectedPatientSources.join(',');
      this.patientSourceDialogVisible = false;
    },

    /** 获取条件标签 */
    getConditionTags(conditionValue, index) {
      if (!conditionValue) {
        return [];
      }

      // 如果条件值为空或只有逗号，返回空数组
      if (conditionValue === ',' || conditionValue === '') {
        return [];
      }

      const values = conditionValue.split(',').filter(v => v !== '');
      if (values.length === 0) {
        return [];
      }

      const tags = [];

      // 根据当前条件类型获取标签
      // 使用传入的index来获取条件
      const condition = this.form.conditions[index];
      // 如果没有找到匹配的条件，返回空数组
      if (!condition) {
        // 将值直接显示为标签
        return values.map(value => ({
          value: value,
          label: value
        }));
      }

      switch (condition.conditionType) {
        case 'dept':
          values.forEach(value => {
            tags.push({
              value: value,
              label: this.deptLabelMap[value] || value
            });
          });
          break;
        case 'body_part':
          values.forEach(value => {
            const option = this.bodyPartOptions.find(o => o.value === value);
            tags.push({
              value: value,
              label: option ? option.label : value
            });
          });
          break;
        case 'patient_source':
          values.forEach(value => {
            const option = this.patientSourceOptions.find(o => o.value === value);
            tags.push({
              value: value,
              label: option ? option.label : value
            });
          });
          break;
        default:
          values.forEach(value => {
            tags.push({
              value: value,
              label: value
            });
          });
      }

      return tags;
    },

    /** 移除条件标签 */
    removeConditionTag(index, value) {
      const condition = this.form.conditions[index];
      if (!condition || !condition.conditionValue) {
        return;
      }

      const values = condition.conditionValue.split(',').filter(v => v !== '');
      const newValues = values.filter(v => v !== value);

      if (newValues.length === 0) {
        this.$message.warning('至少需要保留一个值');
        return;
      }

      condition.conditionValue = newValues.join(',');
    },

    /** 初始化自定义选项 */
    initCustomOptions(conditionType) {
      // 根据条件类型加载不同的选项
      // 这里可以从后端获取数据，这里模拟一些数据
      let options = [];

      switch (conditionType) {
        case 'custom_type1':
          options = [
            { value: 'VALUE1', label: '选项一' },
            { value: 'VALUE2', label: '选项二' },
            { value: 'VALUE3', label: '选项三' }
          ];
          break;
        case 'custom_type2':
          options = [
            { value: 'OPTION1', label: '类型一' },
            { value: 'OPTION2', label: '类型二' },
            { value: 'OPTION3', label: '类型三' }
          ];
          break;
        default:
          options = [
            { value: 'DEFAULT1', label: '默认选项一' },
            { value: 'DEFAULT2', label: '默认选项二' },
            { value: 'DEFAULT3', label: '默认选项三' }
          ];
      }

      this.customOptions = options;
      this.originalCustomOptions = [...options];
    },

    /** 打开自定义选择器 */
    openCustomSelector(index) {
      // 记录当前编辑的条件索引
      this.currentConditionIndex = index;

      // 初始化选项
      const conditionType = this.form.conditions[index].conditionType;
      this.initCustomOptions(conditionType);

      // 获取已选中的自定义值
      const conditionValue = this.form.conditions[index].conditionValue;
      let selectedCustomValues = conditionValue ? conditionValue.split(',').filter(v => v !== '') : [];
      console.log('选中的自定义值:', selectedCustomValues);

      // 显示对话框
      this.customDialogVisible = true;

      // 使用 nextTick 确保对话框已经渲染
      this.$nextTick(() => {
        console.log("进入自定义选择器：")
        if (this.$refs.customTable) {
          // 清除现有选中状态
          this.$refs.customTable.clearSelection();

          // 选中行
          this.customOptions.forEach(row => {
            if (selectedCustomValues.includes(row.value)) {
              this.$refs.customTable.toggleRowSelection(row, true);
            }
          });
        }
      });
    },

    /** 搜索自定义值 */
    searchCustomValues() {
      // 如果搜索值为空，恢复原始选项
      if (!this.customSearchValue) {
        this.customOptions = [...this.originalCustomOptions];
        return;
      }

      // 根据搜索值过滤选项
      const searchValue = this.customSearchValue.toLowerCase();
      this.customOptions = this.originalCustomOptions.filter(option =>
        option.value.toLowerCase().includes(searchValue) ||
        option.label.toLowerCase().includes(searchValue)
      );

      // 使用 nextTick 确保选项已经更新
      this.$nextTick(() => {
        if (this.$refs.customTable) {
          // 清除现有选中状态
          this.$refs.customTable.clearSelection();

          // 选中行
          this.customOptions.forEach(row => {
            if (this.selectedCustomValues.includes(row.value)) {
              this.$refs.customTable.toggleRowSelection(row, true);
            }
          });
        }
      });
    },

    /** 自定义选择器多选框选中数据 */
    handleCustomSelectionChange(selection) {
      this.selectedCustomValues = selection.map(item => item.value);
    },

    /** 处理自定义选择 */
    handleCustomSelect() {
      if (this.selectedCustomValues.length === 0) {
        this.$message.warning('请至少选择一个值');
        return;
      }

      // 更新条件值
      this.form.conditions[this.currentConditionIndex].conditionValue = this.selectedCustomValues.join(',');
      this.customDialogVisible = false;
    },

    /** 获取自定义标签 */
    getCustomTags(conditionValue) {
      if (!conditionValue) {
        return [];
      }

      const values = conditionValue.split(',');
      return values.map(value => ({
        value: value,
        label: value
      }));
    },

    /** 移除自定义标签 */
    removeCustomTag(index, value) {
      const condition = this.form.conditions[index];
      if (!condition || !condition.conditionValue) {
        return;
      }

      const values = condition.conditionValue.split(',').filter(v => v !== '');
      const newValues = values.filter(v => v !== value);

      if (newValues.length === 0) {
        this.$message.warning('至少需要保留一个值');
        return;
      }

      condition.conditionValue = newValues.join(',');
    }
  }
};
</script>

<style scoped>
.condition-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.tag-container {
  min-height: 36px;
  padding: 5px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
}

.condition-value-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.condition-value-container .tag-container {
  flex: 1;
  margin-right: 10px;
  min-height: 50px;
  height: auto;
  overflow: visible;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
}
</style>
