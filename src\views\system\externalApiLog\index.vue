<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="接口代码" prop="apiCode">
        <el-input
          v-model="queryParams.apiCode"
          placeholder="请输入接口代码"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请求状态" clearable style="width: 240px">
          <el-option label="成功" value="0" />
          <el-option label="失败" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务ID" prop="businessId">
        <el-input
          v-model="queryParams.businessId"
          placeholder="请输入业务ID"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:apilog:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleClean"
          v-hasPermi="['system:apilog:clean']"
        >清空</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:apilog:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-back"
          size="mini"
          @click="handleBack"
        >返回</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="日志ID" align="center" prop="logId" />
      <el-table-column label="接口代码" align="center" prop="apiCode" :show-overflow-tooltip="true" />
      <el-table-column label="请求URL" align="center" prop="requestUrl" :show-overflow-tooltip="true" />
      <el-table-column label="请求方法" align="center" prop="requestMethod" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
            {{ scope.row.status === '0' ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="耗时(毫秒)" align="center" prop="costTime" sortable />
      <el-table-column label="业务ID" align="center" prop="businessId" :show-overflow-tooltip="true" />
      <el-table-column label="业务类型" align="center" prop="businessType" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['system:apilog:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:apilog:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看日志详情对话框 -->
    <el-dialog title="日志详情" :visible.sync="open" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="日志ID">{{ form.logId }}</el-descriptions-item>
        <el-descriptions-item label="接口ID">{{ form.apiId }}</el-descriptions-item>
        <el-descriptions-item label="接口代码">{{ form.apiCode }}</el-descriptions-item>
        <el-descriptions-item label="请求方法">{{ form.requestMethod }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="form.status === '0' ? 'success' : 'danger'">
            {{ form.status === '0' ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="耗时(毫秒)">{{ form.costTime }}</el-descriptions-item>
        <el-descriptions-item label="业务ID">{{ form.businessId }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">{{ form.businessType }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(form.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="请求URL" :span="2">{{ form.requestUrl }}</el-descriptions-item>
        <el-descriptions-item label="错误消息" :span="2" v-if="form.errorMsg">{{ form.errorMsg }}</el-descriptions-item>
      </el-descriptions>
      
      <el-tabs v-model="activeTab" type="border-card" style="margin-top: 20px;">
        <el-tab-pane label="请求数据" name="request">
          <pre class="json-content">{{ formatJson(form.requestData) }}</pre>
        </el-tab-pane>
        <el-tab-pane label="响应数据" name="response">
          <pre class="json-content">{{ formatJson(form.responseData) }}</pre>
        </el-tab-pane>
      </el-tabs>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExternalApiLog, getExternalApiLog, delExternalApiLog, cleanExternalApiLog } from "@/api/system/externalApiLog";

export default {
  name: "ExternalApiLog",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 外部接口日志表格数据
      logList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 活动选项卡
      activeTab: 'request',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        apiId: undefined,
        apiCode: undefined,
        status: undefined,
        businessId: undefined
      },
      // 表单参数
      form: {}
    };
  },
  created() {
    // 从路由参数中获取接口ID和代码
    if (this.$route.query.apiId) {
      this.queryParams.apiId = this.$route.query.apiId;
    }
    if (this.$route.query.apiCode) {
      this.queryParams.apiCode = this.$route.query.apiCode;
    }
    this.getList();
  },
  methods: {
    /** 查询外部接口日志列表 */
    getList() {
      this.loading = true;
      listExternalApiLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.logList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 返回按钮操作
    handleBack() {
      this.$router.push('/system/externalApi');
    },
    // 格式化JSON
    formatJson(jsonString) {
      if (!jsonString) {
        return '';
      }
      try {
        const json = JSON.parse(jsonString);
        return JSON.stringify(json, null, 2);
      } catch (e) {
        return jsonString;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      // 保留从路由传入的接口ID和代码
      if (this.$route.query.apiId) {
        this.queryParams.apiId = this.$route.query.apiId;
      }
      if (this.$route.query.apiCode) {
        this.queryParams.apiCode = this.$route.query.apiCode;
      }
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.logId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.form = {};
      const logId = row.logId || this.ids;
      getExternalApiLog(logId).then(response => {
        this.form = response.data;
        this.open = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const logIds = row.logId || this.ids;
      this.$modal.confirm('是否确认删除日志编号为"' + logIds + '"的数据项？').then(() => {
        return delExternalApiLog(logIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 清空按钮操作 */
    handleClean() {
      this.$modal.confirm('是否确认清空所有日志数据？此操作不可恢复！').then(() => {
        return cleanExternalApiLog();
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("清空成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/apilog/export', {
        ...this.queryParams
      }, `api_log_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>

<style scoped>
.json-content {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow: auto;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
