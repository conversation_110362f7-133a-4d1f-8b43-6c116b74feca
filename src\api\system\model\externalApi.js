/**
 * 外部接口对象
 */
export default class ExternalApi {
  constructor() {
    this.apiId = null;
    this.apiName = null;
    this.apiCode = null;
    this.apiUrl = null;
    this.requestMethod = 'POST';
    this.contentType = 'application/json';
    this.timeout = 5000;
    this.retryCount = 3;
    this.retryInterval = 1000;
    this.status = '0';
    this.remark = null;
    this.createBy = null;
    this.createTime = null;
    this.updateBy = null;
    this.updateTime = null;
  }
}

/**
 * 外部接口日志对象
 */
export class ExternalApiLog {
  constructor() {
    this.logId = null;
    this.apiId = null;
    this.apiCode = null;
    this.requestUrl = null;
    this.requestMethod = null;
    this.requestData = null;
    this.responseData = null;
    this.status = '0';
    this.errorMsg = null;
    this.costTime = 0;
    this.businessId = null;
    this.businessType = null;
    this.createTime = null;
  }
}
