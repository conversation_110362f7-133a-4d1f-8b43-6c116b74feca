import request from '@/utils/request'

// 查询申请单详情
export function getStudyRequest(requestId) {
  return request({
    url: '/reservation/studyRequest/' + requestId,
    method: 'get'
  })
}

// 查询申请单列表
export function listStudyRequest(query) {
  return request({
    url: '/reservation/studyRequest/list',
    method: 'get',
    params: query
  })
}

// 同步外部系统申请单
export function syncStudyRequests() {
  return request({
    url: '/reservation/studyRequest/sync',
    method: 'post'
  })
}

// 新增申请单
export function addStudyRequest(data) {
  return request({
    url: '/reservation/studyRequest',
    method: 'post',
    data: data
  })
}

// 修改申请单
export function updateStudyRequest(data) {
  return request({
    url: '/reservation/studyRequest',
    method: 'put',
    data: data
  })
}

// 删除申请单
export function delStudyRequest(requestId) {
  return request({
    url: '/reservation/studyRequest/' + requestId,
    method: 'delete'
  })
}
