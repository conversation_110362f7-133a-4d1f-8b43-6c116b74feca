<template>
  <div class="login">
    <!-- 左侧装饰区域 -->
    <div class="login-left">
      <div class="login-decoration">
        <div class="medical-icon">
          <i class="el-icon-s-custom"></i>
        </div>
        <h1 class="system-title">智慧医疗预约系统</h1>
        <p class="system-subtitle">Medical Appointment System</p>
        <div class="feature-list">
          <div class="feature-item">
            <i class="el-icon-date"></i>
            <span>在线预约挂号</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-time"></i>
            <span>智能排班管理</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-monitor"></i>
            <span>设备资源调度</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-s-tools"></i>
            <span>规则可视配置</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单 -->
    <div class="login-right">
      <div class="login-container">
        <div class="login-header">
          <div class="logo-section">
            <div class="logo-icon">
              <i class="el-icon-s-custom"></i>
            </div>
            <h2 class="login-title">系统登录</h2>
          </div>
          <p class="login-subtitle">欢迎使用医疗预约管理系统</p>
        </div>

        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              auto-complete="off"
              placeholder="请输入用户名"
              size="large"
            >
              <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              auto-complete="off"
              placeholder="请输入密码"
              size="large"
              @keyup.enter.native="handleLogin"
            >
              <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <el-form-item prop="code" v-if="captchaEnabled">
            <el-input
              v-model="loginForm.code"
              auto-complete="off"
              placeholder="请输入验证码"
              size="large"
              style="width: 60%"
              @keyup.enter.native="handleLogin"
            >
              <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img"/>
            </div>
          </el-form-item>

          <div class="login-options">
            <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
            <router-link v-if="register" class="forgot-password" :to="'/register'">
              立即注册
            </router-link>
          </div>

          <el-form-item style="width:100%; margin-top: 30px;">
            <el-button
              :loading="loading"
              size="large"
              type="primary"
              class="login-btn"
              @click.native.prevent="handleLogin"
            >
              <span v-if="!loading">立即登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!--  底部版权信息  -->
    <div class="el-login-footer">
      <span>Copyright © 2024 智慧医疗预约系统 All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "admin",
        password: "admin123",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
          }).catch(() => {
            this.loading = false;
            if (this.captchaEnabled) {
              this.getCode();
            }
          });
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    opacity: 0.8;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="medical" patternUnits="userSpaceOnUse" width="60" height="60"><circle cx="30" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><path d="M25,30 L35,30 M30,25 L30,35" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="60" height="60" fill="url(%23medical)"/></svg>');
    opacity: 0.4;
  }
}

// 左侧装饰区域
.login-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;

  .login-decoration {
    text-align: center;
    color: white;
    max-width: 500px;
    padding: 0 40px;

    .medical-icon {
      width: 120px;
      height: 120px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 30px;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.3);
      animation: pulse 3s ease-in-out infinite;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        animation: ripple 3s ease-in-out infinite;
      }

      i {
        font-size: 60px;
        color: white;
        animation: float 3s ease-in-out infinite;
      }
    }

    .system-title {
      font-size: 42px;
      font-weight: 700;
      margin-bottom: 15px;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      letter-spacing: 2px;
    }

    .system-subtitle {
      font-size: 18px;
      margin-bottom: 50px;
      opacity: 0.9;
      font-weight: 300;
      letter-spacing: 1px;
    }

    .feature-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 25px;
      margin-top: 40px;

      .feature-item {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.25);
          transform: translateY(-2px);
        }

        i {
          font-size: 24px;
          margin-right: 12px;
          color: #fff;
        }

        span {
          font-size: 16px;
          font-weight: 500;
        }
      }
    }
  }
}

// 右侧登录区域
.login-right {
  width: 480px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;

  .login-container {
    width: 100%;
    max-width: 380px;
    padding: 40px;
  }

  .login-header {
    text-align: center;
    margin-bottom: 40px;

    .logo-section {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;

      .logo-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        i {
          font-size: 24px;
          color: white;
        }
      }

      .login-title {
        font-size: 28px;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
      }
    }

    .login-subtitle {
      color: #7f8c8d;
      font-size: 16px;
      margin: 0;
      font-weight: 400;
    }
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 25px;
  }

  .el-input {
    .el-input__inner {
      height: 50px;
      border-radius: 12px;
      border: 2px solid #e8ecf0;
      font-size: 16px;
      padding-left: 50px;
      transition: all 0.3s ease;

      &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
  }

  .input-icon {
    height: 50px;
    width: 20px;
    margin-left: 15px;
    color: #bdc3c7;
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;

  .el-checkbox {
    .el-checkbox__label {
      color: #7f8c8d;
      font-size: 14px;
    }
  }

  .forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;

    &:hover {
      color: #5a6fd8;
    }
  }
}

.login-btn {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
  }
}

.login-code {
  width: 38%;
  height: 50px;
  float: right;
  margin-left: 10px;

  .login-code-img {
    width: 100%;
    height: 50px;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid #e8ecf0;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
    }
  }
}

.el-login-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  z-index: 10;
}

// 响应式设计
@media (max-width: 768px) {
  .login {
    flex-direction: column;
  }

  .login-left {
    display: none;
  }

  .login-right {
    width: 100%;

    .login-container {
      padding: 20px;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.login-left {
  animation: slideInLeft 1s ease-out;
}

.login-right {
  animation: slideInRight 1s ease-out;
}

.login-container {
  animation: fadeInUp 0.8s ease-out 0.3s both;
}

.feature-item {
  animation: fadeInUp 0.8s ease-out both;

  &:nth-child(1) { animation-delay: 0.5s; }
  &:nth-child(2) { animation-delay: 0.6s; }
  &:nth-child(3) { animation-delay: 0.7s; }
  &:nth-child(4) { animation-delay: 0.8s; }
}
</style>
