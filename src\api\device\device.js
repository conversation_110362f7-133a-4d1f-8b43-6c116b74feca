import request from '@/utils/request'

// 查询检查设备列表
export function listModality(query) {
  return request({
    url: '/device/list',
    method: 'get',
    params: query
  })
}

// 查询检查设备详细
export function getModality(modalityUid) {
  return request({
    url: '/device/' + modalityUid,
    method: 'get'
  })
}

// 新增检查设备
export function addModality(data) {
  return request({
    url: '/device',
    method: 'post',
    data: data
  })
}

// 修改检查设备
export function updateModality(data) {
  return request({
    url: '/device',
    method: 'put',
    data: data
  })
}

// 删除检查设备
export function delModality(modalityUid) {
  return request({
    url: '/device/' + modalityUid,
    method: 'delete'
  })
}
