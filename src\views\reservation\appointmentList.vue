<!-- 已预约查询 -->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="患者姓名" prop="patientName">
        <el-input
          v-model="queryParams.patientName"
          placeholder="请输入患者姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="患者编号" prop="hisPatientId">
        <el-input
          v-model="queryParams.hisPatientId"
          placeholder="请输入患者编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="预约日期" prop="appointmentDate">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="预约状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['reservation:appointment:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['reservation:appointment:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['reservation:appointment:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['reservation:appointment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="appointmentList" @selection-change="handleSelectionChange" style="width: 100%;" :header-cell-style="{backgroundColor: '#fafafa'}" border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="预约ID" align="center" prop="appointmentId" width="80" />
      <el-table-column label="患者姓名" align="center" prop="patientName" width="100" />
      <el-table-column label="患者编号" align="center" prop="hisPatientId" min-width="120" :show-overflow-tooltip="true" />
      <el-table-column label="性别" align="center" prop="patientSex" width="60">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.patientSex"/>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="patientPhone" width="120" />
      <el-table-column label="设备名称" align="center" min-width="120" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.device ? scope.row.device.deviceName : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备地址" align="center" min-width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.device ? scope.row.device.deviceLocation : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预约日期" align="center" prop="appointmentDate" width="100" />
      <el-table-column label="预约时间" align="center" prop="appointmentTime" width="120" />
      <el-table-column label="预约类型" align="center" prop="planType" width="80">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.planType === 0 || scope.row.planType === '0'">普通号</el-tag>
          <el-tag type="danger" v-if="scope.row.planType === 1 || scope.row.planType === '1'">急诊号</el-tag>
          <el-tag type="primary" v-if="scope.row.planType === 2 || scope.row.planType === '2'">保留号</el-tag>
          <span v-if="scope.row.planType === undefined || scope.row.planType === null">{{ scope.row.planType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag type="info" v-if="scope.row.status === 0 || scope.row.status === '0'">待检查</el-tag>
          <el-tag type="success" v-if="scope.row.status === 1 || scope.row.status === '1'">已检查</el-tag>
          <el-tag type="danger" v-if="scope.row.status === 2 || scope.row.status === '2'">已取消</el-tag>
          <span v-if="scope.row.status === undefined || scope.row.status === null">{{ scope.row.status }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['reservation:appointment:query']"
          >详情</el-button>
          <el-button
            v-if="scope.row.status === 0 || scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleChange(scope.row)"
            v-hasPermi="['reservation:appointment:edit']"
          >更改预约</el-button>
          <el-button
            v-if="scope.row.status === 0 || scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleCancel(scope.row)"
            v-hasPermi="['reservation:appointment:edit']"
          >取消预约</el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 预约详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="患者姓名">
              <el-input v-model="form.patientName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="患者编号">
              <el-input v-model="form.hisPatientId" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="联系电话">
              <el-input v-model="form.patientPhone" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="设备名称">
              <el-input :value="getDeviceName()" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="设备地址">
              <el-input :value="getDeviceLocation()" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预约日期">
              <el-input v-model="form.appointmentDate" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预约时间">
              <el-input v-model="form.appointmentTime" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预约类型">
              <el-tag type="success" v-if="form.planType === 0 || form.planType === '0'">普通号</el-tag>
              <el-tag type="danger" v-if="form.planType === 1 || form.planType === '1'">急诊号</el-tag>
              <el-tag type="primary" v-if="form.planType === 2 || form.planType === '2'">保留号</el-tag>
              <span v-if="form.planType === undefined || form.planType === null">{{ form.planType }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-tag type="info" v-if="form.status === 0 || form.status === '0'">待检查</el-tag>
              <el-tag type="success" v-if="form.status === 1 || form.status === '1'">已检查</el-tag>
              <el-tag type="danger" v-if="form.status === 2 || form.status === '2'">已取消</el-tag>
              <span v-if="form.status === undefined || form.status === null">{{ form.status }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.status === 2 || form.status === '2'">
            <el-form-item label="取消原因">
              <el-input v-model="form.cancelReason" type="textarea" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 更改预约对话框 -->
    <el-dialog :title="'更改预约 - ' + changeForm.patientName" :visible.sync="changeOpen" width="70%" append-to-body :close-on-click-modal="false">
      <el-row :gutter="20" class="change-reservation-container">
        <!-- 左侧区域：当前预约信息和新预约设置 -->
        <el-col :span="8" class="left-panel">
          <el-form ref="changeForm" :model="changeForm" label-width="80px">
            <!-- 当前预约信息 -->
            <div class="current-appointment-info">
              <div class="section-title">当前预约信息</div>
              <div class="info-item">
                <span class="label">患者姓名：</span>
                <span class="value">{{ changeForm.patientName }}</span>
              </div>
              <div class="info-item">
                <span class="label">执行科室：</span>
                <span class="value">{{ changeForm.studyPerformdept || '未指定' }}</span>
              </div>
              <div class="info-item">
                <span class="label">设备名称：</span>
                <span class="value">{{ changeForm.deviceName }}</span>
              </div>
              <div class="info-item">
                <span class="label">预约日期：</span>
                <span class="value">{{ changeForm.currentDate }}</span>
              </div>
              <div class="info-item">
                <span class="label">预约时间：</span>
                <span class="value">{{ changeForm.currentTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">预约类型：</span>
                <span class="value">
                  <el-tag type="success" size="small" v-if="changeForm.currentPlanType === 0 || changeForm.currentPlanType === '0'">普通号</el-tag>
                  <el-tag type="danger" size="small" v-if="changeForm.currentPlanType === 1 || changeForm.currentPlanType === '1'">急诊号</el-tag>
                  <el-tag type="primary" size="small" v-if="changeForm.currentPlanType === 2 || changeForm.currentPlanType === '2'">保留号</el-tag>
                </span>
              </div>
            </div>

            <el-divider></el-divider>

            <!-- 新预约设置 -->
            <div class="new-appointment-settings">
              <div class="section-title">选择新的预约时间</div>

              <!-- 设备选择 -->
              <div class="device-selection">
                <el-form-item label="设备" prop="newDeviceId">
                  <div class="device-selection-tip" v-if="changeForm.studyPerformdept">
                    <i class="el-icon-info"></i>
                    <span>只能选择 "{{ changeForm.studyPerformdept }}" 科室的设备</span>
                  </div>
                  <el-select
                    v-model="changeForm.newDeviceId"
                    placeholder="请选择设备"
                    clearable
                    @change="handleDeviceChange"
                    style="width: 100%"
                    :disabled="deviceOptions.length === 0">
                    <el-option
                      v-for="device in deviceOptions"
                      :key="device.deviceId"
                      :label="device.deviceName"
                      :value="device.deviceId">
                      <span style="float: left">{{ device.deviceName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ device.deviceLocation || '未设置位置' }}</span>
                    </el-option>
                  </el-select>
                  <div v-if="deviceOptions.length === 0" class="no-devices-tip">
                    <i class="el-icon-warning"></i>
                    <span>{{ changeForm.studyPerformdept ? changeForm.studyPerformdept + ' 科室暂无可用设备' : '暂无可用设备' }}</span>
                  </div>
                </el-form-item>
              </div>

              <!-- 预约日期选择 -->
              <el-form-item label="预约日期" prop="newDate">
                <el-date-picker
                  v-model="changeForm.newDate"
                  type="date"
                  placeholder="选择预约日期"
                  value-format="yyyy-MM-dd"
                  :picker-options="datePickerOptions"
                  @change="handleDateChange"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>

              <!-- 选择结果显示 -->
              <div v-if="changeForm.selectedTimeSlot" class="selected-appointment-info">
                <div class="section-title">新预约信息</div>
                <div class="info-item">
                  <span class="label">设备：</span>
                  <span class="value">{{ getSelectedDeviceName() }}</span>
                </div>
                <div class="info-item">
                  <span class="label">日期：</span>
                  <span class="value">{{ changeForm.newDate }}</span>
                </div>
                <div class="info-item">
                  <span class="label">时间：</span>
                  <span class="value">{{ changeForm.selectedTimeSlot.startTime }} - {{ changeForm.selectedTimeSlot.endTime }}</span>
                </div>
                <div class="info-item">
                  <span class="label">类型：</span>
                  <span class="value">
                    <el-tag type="success" size="small" v-if="changeForm.newPlanType === 0">普通号</el-tag>
                    <el-tag type="danger" size="small" v-if="changeForm.newPlanType === 1">急诊号</el-tag>
                    <el-tag type="primary" size="small" v-if="changeForm.newPlanType === 2">保留号</el-tag>
                  </span>
                </div>
              </div>
            </div>
          </el-form>
        </el-col>

        <!-- 右侧区域：可用时间段 -->
        <el-col :span="16" class="right-panel">
          <div class="schedule-container">
            <div v-if="!changeForm.newDeviceId" class="no-date-selected">
              <i class="el-icon-monitor"></i>
              <span>请先选择设备</span>
            </div>
            <div v-else-if="!changeForm.newDate" class="no-date-selected">
              <i class="el-icon-date"></i>
              <span>请选择预约日期查看可用时间段</span>
            </div>
            <div v-else-if="availableTimeSlots.length === 0" class="no-schedules">
              <i class="el-icon-time"></i>
              <span>所选日期没有可用的排班计划</span>
            </div>
            <div v-else class="time-slots-wrapper">
              <div class="section-title">
                <div class="title-with-type">
                  <span>可预约时间段</span>
                  <div class="current-plan-type" v-if="changeForm.newPlanType !== null && changeForm.selectedTimeSlot">
                    <span class="plan-type-label">当前选择：</span>
                    <el-tag size="small" type="success" v-if="changeForm.newPlanType === 0">普通号</el-tag>
                    <el-tag size="small" type="danger" v-if="changeForm.newPlanType === 1">急诊号</el-tag>
                    <el-tag size="small" type="primary" v-if="changeForm.newPlanType === 2">保留号</el-tag>
                  </div>
                </div>
              </div>
              <div class="time-slots">
                <el-card v-for="(slot, index) in availableTimeSlots" :key="index"
                  :class="{ 'selected-slot': slot.selected, 'disabled-slot': slot.disabled }" shadow="hover">
                  <div class="time-range">
                    <div class="time-display">{{ slot.startTime }} - {{ slot.endTime }}</div>
                    <div v-if="slot.disabled && slot.disabledReason" class="disabled-reason">
                      <el-tooltip :content="slot.disabledReasonFull || slot.disabledReason" placement="top"
                        :disabled="!slot.disabledReasonFull">
                        <span class="disabled-reason-text">{{ slot.disabledReason }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="remaining-counts">
                    <div class="remaining-count-item" @click.stop="!slot.disabled && selectPlanType(slot, 0)">
                      <el-tag type="success" size="small"
                        :class="{ 'selected-type': changeForm.newPlanType === 0 && slot.selected, 'disabled-tag': slot.disabled || slot.ordinaryRemaining <= 0 }">
                        普通号: <span class="count">{{ slot.ordinaryCount || 0 }}/{{ slot.ordinaryPlan }}</span>
                      </el-tag>
                    </div>
                    <div class="remaining-count-item" @click.stop="!slot.disabled && selectPlanType(slot, 1)">
                      <el-tag type="danger" size="small"
                        :class="{ 'selected-type': changeForm.newPlanType === 1 && slot.selected, 'disabled-tag': slot.disabled || slot.emergencyRemaining <= 0 }">
                        急诊号: <span class="count">{{ slot.emergencyCount || 0 }}/{{ slot.emergencyPlan }}</span>
                      </el-tag>
                    </div>
                    <div class="remaining-count-item" @click.stop="!slot.disabled && selectPlanType(slot, 2)"
                      v-if="slot.reservePlan != -1">
                      <el-tag type="primary" size="small"
                        :class="{ 'selected-type': changeForm.newPlanType === 2 && slot.selected, 'disabled-tag': slot.disabled || slot.reserveRemaining <= 0 }">
                        保留号: <span class="count">{{ slot.reserveCount || 0 }}/{{ slot.reservePlan }}</span>
                      </el-tag>
                    </div>
                    <div class="remaining-count-item total">
                      <el-tag size="small">
                        总计: <span class="count" v-if="slot.reservePlan == -1">{{ slot.totalCount || 0 }}/{{
                          slot.ordinaryPlan + slot.emergencyPlan }} </span>
                        <span class="count" v-else>{{ slot.totalCount || 0 }}/{{ slot.totalPlan }}</span>
                      </el-tag>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitChange" :disabled="!changeForm.selectedTimeSlot">确认更改</el-button>
        <el-button @click="cancelChange">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 取消预约对话框 -->
    <el-dialog title="取消预约" :visible.sync="cancelOpen" width="500px" append-to-body>
      <el-form ref="cancelForm" :model="cancelForm" :rules="cancelRules" label-width="100px">
        <el-form-item label="取消原因" prop="cancelReason">
          <el-input v-model="cancelForm.cancelReason" type="textarea" placeholder="请输入取消原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCancel">确 定</el-button>
        <el-button @click="cancelOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAppointment, getAppointment, updateAppointment, delAppointment, changeAppointment, getAppointmentCount } from "@/api/reservation/appointment";
import { listDevicePlan } from "@/api/device/plan";
import { listModality } from "@/api/device/device";
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "AppointmentList",
  dicts: ['sys_user_sex'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预约表格数据
      appointmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示取消预约弹出层
      cancelOpen: false,
      // 是否显示更改预约弹出层
      changeOpen: false,
      // 日期范围
      dateRange: [],
      // 状态选项
      statusOptions: [
        { value: 0, label: '待检查' },
        { value: 1, label: '已检查' },
        { value: 2, label: '已取消' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        patientName: undefined,
        hisPatientId: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 取消预约表单
      cancelForm: {
        appointmentId: null,
        cancelReason: ""
      },
      // 取消预约表单校验规则
      cancelRules: {
        cancelReason: [
          { required: true, message: "取消原因不能为空", trigger: "blur" }
        ]
      },
      // 更改预约表单
      changeForm: {
        appointmentId: null,
        patientName: "",
        deviceName: "",
        deviceId: null,
        currentDate: "",
        currentTime: "",
        currentPlanType: null,
        newDate: "",
        newDeviceId: null,
        selectedTimeSlot: null,
        newPlanType: null
      },
      // 设备选项
      deviceOptions: [],
      // 可用时间段
      availableTimeSlots: [],
      // 日期选择器配置
      datePickerOptions: {
        disabledDate(time) {
          // 禁用今天之前的日期
          return time.getTime() < Date.now() - 8.64e7;
        }
      }
    };
  },
  created() {
    this.getList();
    this.getDeviceList();
  },
  methods: {
    /** 查询预约列表 */
    getList() {
      this.loading = true;
      listAppointment(this.addDateRange(this.queryParams, this.dateRange, 'appointmentDate')).then(response => {
        this.appointmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.appointmentId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // 使用 router.push 而不是直接跳转，保持导航栏和侧边栏
      this.$router.push({ path: '/reservation/studyRequest' });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const appointmentId = row.appointmentId || this.ids;
      getAppointment(appointmentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改预约";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.reset();
      const appointmentId = row.appointmentId || this.ids;
      // getAppointment(appointmentId).then(response => {
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "预约详情";
      // });
      this.form = row

      this.open = true;
      this.title = "预约详情";
    },

    /** 更改预约按钮操作 */
    handleChange(row) {
      // 获取今天日期
      const today = new Date();
      const todayStr = today.getFullYear() + '-' +
                      String(today.getMonth() + 1).padStart(2, '0') + '-' +
                      String(today.getDate()).padStart(2, '0');

      this.changeForm = {
        appointmentId: row.appointmentId,
        patientName: row.patientName,
        deviceName: row.device ? row.device.deviceName : '',
        deviceId: row.device ? row.device.deviceId : null,
        currentDate: row.appointmentDate,
        currentTime: row.appointmentTime,
        currentPlanType: row.planType,
        studyPerformdeptid: row.studyPerformdeptid,
        studyPerformdept: row.studyPerformdept,
        newDate: todayStr, // 默认设置为今天
        newDeviceId: null,
        selectedTimeSlot: null,
        newPlanType: null
      };
      this.availableTimeSlots = [];
      this.changeOpen = true;

      // 根据执行科室获取设备列表
      if (row.studyPerformdeptid) {
        this.getDeviceListByDept(row.studyPerformdeptid);
      } else {
        this.deviceOptions = [];
        this.$modal.msgWarning('当前预约缺少执行科室信息，无法更改设备');
      }
    },

    /** 取消预约按钮操作 */
    handleCancel(row) {
      this.cancelForm = {
        appointmentId: row.appointmentId,
        cancelReason: ""
      };
      this.cancelOpen = true;
    },

    /** 提交取消预约 */
    submitCancel() {
      this.$refs["cancelForm"].validate(valid => {
        if (valid) {
          this.loading = true;
          // 更新预约状态为已取消
          const data = {
            appointmentId: this.cancelForm.appointmentId,
            status: 2, // 已取消
            cancelReason: this.cancelForm.cancelReason
          };
          updateAppointment(data).then(() => {
            this.$modal.msgSuccess("取消预约成功");
            this.cancelOpen = false;
            this.getList();
            this.loading = false;
          }).catch(() => {
            this.loading = false;
          });
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const appointmentIds = row.appointmentId || this.ids;
      this.$modal.confirm('是否确认删除预约编号为"' + appointmentIds + '"的数据项？').then(() => {
        this.loading = true;
        return delAppointment(appointmentIds);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('reservation/appointment/export', {
        ...this.queryParams
      }, `appointment_${new Date().getTime()}.xlsx`);
    },

    /** 获取设备名称 */
    getDeviceName() {
      return this.form.device ? this.form.device.deviceName : '';
    },

    /** 获取设备地址 */
    getDeviceLocation() {
      return this.form.device ? this.form.device.deviceLocation : '';
    },

    /** 获取设备列表 */
    async getDeviceList() {
      try {
        this.loading = true;
        const response = await listModality();
        this.deviceOptions = response.rows || [];
        return this.deviceOptions;
      } catch (error) {
        console.error("获取设备列表失败:", error);
        this.$modal.msgError("获取设备列表失败");
        return [];
      } finally {
        this.loading = false;
      }
    },

    /** 根据科室获取设备列表 */
    async getDeviceListByDept(deptId) {
      try {
        this.loading = true;
        const response = await listModality({ deptId: deptId });
        this.deviceOptions = response.rows || [];

        if (this.deviceOptions.length === 0) {
          this.$modal.msgWarning('该执行科室暂无可用设备');
        }

        return this.deviceOptions;
      } catch (error) {
        console.error("获取科室设备列表失败:", error);
        this.$modal.msgError("获取科室设备列表失败");
        return [];
      } finally {
        this.loading = false;
      }
    },

    /** 设备变更事件 */
    handleDeviceChange() {
      this.clearSelectedTimeSlot();

      // 更新设备ID
      this.changeForm.deviceId = this.changeForm.newDeviceId;

      if (this.changeForm.newDate) {
        this.getAvailableTimeSlots();
      } else {
        this.availableTimeSlots = [];
      }
    },

    /** 获取选中的设备名称 */
    getSelectedDeviceName() {
      if (!this.changeForm.newDeviceId) return '';
      const selectedDevice = this.deviceOptions.find(device => device.deviceId === this.changeForm.newDeviceId);
      return selectedDevice ? selectedDevice.deviceName : '';
    },

    /** 处理日期变更 */
    async handleDateChange() {
      // 清空之前的选择
      this.clearSelectedTimeSlot();

      if (!this.changeForm.newDate || !this.changeForm.deviceId) {
        this.availableTimeSlots = [];
        return;
      }

      try {
        this.loading = true;
        await this.getAvailableTimeSlots();
      } catch (error) {
        console.error('获取时间段失败:', error);
        this.$modal.msgError('获取时间段失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },

    /** 清空选中的时间段 */
    clearSelectedTimeSlot() {
      this.changeForm.selectedTimeSlot = null;
      this.changeForm.newPlanType = null;

      // 清空所有时间段的选中状态
      if (this.availableTimeSlots?.length > 0) {
        this.availableTimeSlots.forEach(slot => {
          slot.selected = false;
        });
      }
    },

    /** 获取可用时间段 */
    async getAvailableTimeSlots() {
      if (!this.validateTimeSlotParams()) {
        return;
      }

      try {
        // 获取设备计划列表
        const plans = await this.fetchDevicePlans();

        // 创建时间段对象
        this.availableTimeSlots = plans.map(plan => this.createTimeSlot(plan));

        // 获取预约数量并更新时间段
        await this.updateTimeSlotsWithCounts();

      } catch (error) {
        console.error('获取时间段失败:', error);
        this.$modal.msgError('获取时间段失败，请稍后重试');
      }
    },

    /** 验证时间段参数 */
    validateTimeSlotParams() {
      return this.changeForm.deviceId && this.changeForm.newDate;
    },

    /** 获取设备计划 */
    async fetchDevicePlans() {
      const params = {
        deviceId: this.changeForm.deviceId,
        planDate: this.changeForm.newDate
      };

      const response = await listDevicePlan(params);
      return response.rows || [];
    },

    /** 创建时间段对象 */
    createTimeSlot(plan) {
      const timeSlot = {
        startTime: plan.stime,
        endTime: plan.etime,
        planId: plan.planId,
        disabled: false,
        selected: false,
        ordinaryPlan: plan.ordinaryPlan,
        emergencyPlan: plan.emergencyPlan,
        reservePlan: plan.reservePlan,
        totalPlan: plan.totalPlan,
        ordinaryCount: 0,
        emergencyCount: 0,
        reserveCount: 0,
        totalCount: 0
      };

      // 检查是否已过期
      if (this.isTimeSlotExpired(plan)) {
        timeSlot.disabled = true;
        timeSlot.disabledReason = '已过期';
      }

      return timeSlot;
    },

    /** 检查时间段是否已过期 */
    isTimeSlotExpired(plan) {
      const now = new Date();
      const planDate = new Date(this.changeForm.newDate + ' ' + plan.etime);
      return planDate < now;
    },

    /** 更新时间段预约数量 */
    async updateTimeSlotsWithCounts() {
      const countPromises = this.availableTimeSlots.map(timeSlot =>
        this.fetchTimeSlotCount(timeSlot)
      );

      await Promise.all(countPromises);
    },

    /** 获取单个时间段的预约数量 */
    async fetchTimeSlotCount(timeSlot) {
      try {
        const countParams = {
          deviceId: this.changeForm.deviceId,
          planId: timeSlot.planId,
          appointmentDate: this.changeForm.newDate,
          _t: new Date().getTime()
        };

        const countResponse = await getAppointmentCount(countParams);

        if (countResponse.code === 200 && countResponse.data) {
          this.updateTimeSlotCounts(timeSlot, countResponse.data);
        } else {
          // 使用默认值
          this.setDefaultTimeSlotCounts(timeSlot);
        }
      } catch (error) {
        // 使用默认值
        this.setDefaultTimeSlotCounts(timeSlot);
      }
    },

    /** 更新时间段数量 */
    updateTimeSlotCounts(timeSlot, countData) {
      countData.forEach(item => {
        if (item.planId === timeSlot.planId) {
          const count = item.appointmentCount || 0;
          switch (item.planType) {
            case 0: // 普通号
              timeSlot.ordinaryCount = count;
              break;
            case 1: // 急诊号
              timeSlot.emergencyCount = count;
              break;
            case 2: // 保留号
              timeSlot.reserveCount = count;
              break;
          }
        }
      });

      // 计算剩余数量
      timeSlot.ordinaryRemaining = Math.max(0, timeSlot.ordinaryPlan - timeSlot.ordinaryCount);
      timeSlot.emergencyRemaining = Math.max(0, timeSlot.emergencyPlan - timeSlot.emergencyCount);
      timeSlot.reserveRemaining = Math.max(0, timeSlot.reservePlan - timeSlot.reserveCount);
      timeSlot.remainingCount = timeSlot.ordinaryRemaining + timeSlot.emergencyRemaining + timeSlot.reserveRemaining;

      // 检查是否已约满
      if (timeSlot.remainingCount <= 0) {
        timeSlot.disabled = true;
        timeSlot.disabledReason = '已约满';
      }
    },

    /** 设置时间段默认数量 */
    setDefaultTimeSlotCounts(timeSlot) {
      timeSlot.ordinaryRemaining = timeSlot.ordinaryPlan;
      timeSlot.emergencyRemaining = timeSlot.emergencyPlan;
      timeSlot.reserveRemaining = timeSlot.reservePlan;
      timeSlot.remainingCount = timeSlot.totalPlan;
    },

    /** 选择时间段 */
    selectTimeSlot(slot) {
      if (slot.disabled) return;

      // 验证号源是否充足
      if (!this.validateSlotAvailability(slot)) {
        return;
      }

      // 更新时间段选择状态
      this.updateTimeSlotSelection(slot);

      // 保存时间段选择
      this.saveTimeSlotSelection(slot);
    },

    /** 验证时间段可用性 */
    validateSlotAvailability(slot) {
      // 检查是否有任何可用的号源
      const hasAvailableSlots = slot.ordinaryRemaining > 0 ||
                               slot.emergencyRemaining > 0 ||
                               slot.reserveRemaining > 0;

      if (!hasAvailableSlots) {
        this.$modal.msgWarning('该时间段已经预约满了，请选择其他时间段');
        return false;
      }

      return true;
    },

    /** 更新时间段选择状态 */
    updateTimeSlotSelection(slot) {
      // 取消之前选择的时间段
      this.availableTimeSlots.forEach(item => {
        item.selected = false;
      });

      // 选中当前时间段
      slot.selected = true;
    },

    /** 保存时间段选择 */
    saveTimeSlotSelection(slot) {
      this.changeForm.selectedTimeSlot = slot;

      // 自动选择可用的预约类型（优先普通号）
      if (slot.ordinaryRemaining > 0) {
        this.changeForm.newPlanType = 0;
      } else if (slot.emergencyRemaining > 0) {
        this.changeForm.newPlanType = 1;
      } else if (slot.reserveRemaining > 0) {
        this.changeForm.newPlanType = 2;
      }
    },

    /** 选择预约类型 */
    selectPlanType(slot, planType) {
      if (slot.disabled) return;

      // 验证号源是否充足
      if (!this.validatePlanTypeAvailability(slot, planType)) {
        return;
      }

      // 如果时间段还没有被选中，则选中它
      if (!slot.selected) {
        this.updateTimeSlotSelection(slot);
        this.changeForm.selectedTimeSlot = slot;
      }

      // 设置预约类型
      this.changeForm.newPlanType = planType;
    },

    /** 验证预约类型可用性 */
    validatePlanTypeAvailability(slot, planType) {
      const typeLabels = {
        0: '普通号',
        1: '急诊号',
        2: '保留号'
      };

      const remainingCounts = {
        0: slot.ordinaryRemaining,
        1: slot.emergencyRemaining,
        2: slot.reserveRemaining
      };

      if (remainingCounts[planType] <= 0) {
        this.$modal.msgWarning(`${typeLabels[planType]}已经预约满了，请选择其他类型`);
        return false;
      }

      return true;
    },

    /** 提交更改预约 */
    async submitChange() {
      if (!this.changeForm.selectedTimeSlot) {
        this.$modal.msgWarning('请选择新的预约时间段');
        return;
      }

      try {
        this.loading = true;

        const data = {
          newPlanId: this.changeForm.selectedTimeSlot.planId,
          newPlanType: this.changeForm.newPlanType
        };

        await changeAppointment(this.changeForm.appointmentId, data);

        this.$modal.msgSuccess("更改预约成功");
        this.changeOpen = false;
        this.getList();
      } catch (error) {
        console.error('更改预约失败:', error);
        this.$modal.msgError('更改预约失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },

    /** 取消更改预约 */
    cancelChange() {
      this.changeOpen = false;
      this.resetChangeForm();
    },

    /** 重置更改预约表单 */
    resetChangeForm() {
      this.changeForm = {
        appointmentId: null,
        patientName: "",
        deviceName: "",
        deviceId: null,
        currentDate: "",
        currentTime: "",
        currentPlanType: null,
        studyPerformdeptid: null,
        studyPerformdept: "",
        newDate: "",
        newDeviceId: null,
        selectedTimeSlot: null,
        newPlanType: null
      };
      this.availableTimeSlots = [];
      this.deviceOptions = [];
    }
  }
};
</script>

<style scoped>
.el-tag {
  margin-right: 5px;
}

.app-container {
  padding: 20px;
  height: 100%;
  width: 100%;
}

.el-table {
  margin-top: 15px;
}

/* 更改预约对话框样式 */
.change-reservation-container {
  min-height: 500px;
}

.left-panel {
  border-right: 1px solid #e4e7ed;
  padding-right: 15px;
  height: 100%;
  overflow-y: auto;
}

.right-panel {
  padding-left: 15px;
  height: 100%;
  overflow-y: auto;
}

/* 信息展示区域样式 */
.current-appointment-info,
.new-appointment-settings,
.selected-appointment-info {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  color: #303133;
  margin-bottom: 15px;
}

.info-item {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.info-item .label {
  color: #606266;
  font-weight: bold;
}

.info-item .value {
  color: #303133;
}

/* 设备选择样式 */
.device-selection {
  margin-bottom: 15px;
}

.device-selection-tip {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 13px;
  color: #1890ff;
}

.device-selection-tip i {
  margin-right: 6px;
  font-size: 14px;
}

.no-devices-tip {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #fff2e8;
  border: 1px solid #ffbb96;
  border-radius: 4px;
  margin-top: 10px;
  font-size: 13px;
  color: #fa8c16;
}

.no-devices-tip i {
  margin-right: 6px;
  font-size: 14px;
}

/* 时间段选择区域样式 */
.schedule-container {
  min-height: 400px;
}

.no-date-selected,
.no-schedules {
  text-align: center;
  padding: 100px 0;
  color: #909399;
}

.no-date-selected i,
.no-schedules i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.time-slots-wrapper .section-title {
  margin-bottom: 15px;
}

.title-with-type {
  display: flex;
  align-items: center;
  gap: 15px;
}

.current-plan-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.plan-type-label {
  font-size: 14px;
  color: #606266;
  font-weight: normal;
}

/* 时间段卡片样式 */
.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.time-slots .el-card {
  cursor: pointer;
  transition: all 0.3s;
}

.time-slots .el-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.time-slots .el-card.selected-slot {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.time-slots .el-card.disabled-slot {
  cursor: not-allowed;
  opacity: 0.6;
}

.time-range {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.time-display {
  margin-bottom: 5px;
}

.disabled-reason {
  display: inline-block;
  font-size: 12px;
  color: #F56C6C;
  background-color: #FEF0F0;
  padding: 2px 6px;
  border-radius: 2px;
  font-weight: normal;
  text-align: center;
  width: 100%;
}

.disabled-reason-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: help;
}

.remaining-counts {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 10px;
}

.remaining-count-item {
  display: flex;
  justify-content: space-between;
}

.remaining-count-item .el-tag {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 5px;
}

.remaining-count-item .count {
  font-weight: bold;
}

.remaining-count-item.total {
  margin-top: 5px;
}

.remaining-count-item .el-tag.selected-type {
  font-weight: bold;
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

.remaining-count-item .el-tag:hover {
  cursor: pointer;
  transform: scale(1.02);
  transition: all 0.2s;
}

.remaining-count-item .el-tag.disabled-tag {
  cursor: not-allowed;
  opacity: 0.6;
}

.remaining-count-item .el-tag.disabled-tag:hover {
  cursor: not-allowed;
  transform: none;
}
</style>
