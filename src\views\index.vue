<template>
  <div class="dashboard-container">
    <!-- 顶部数据卡片 -->
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-icon-wrapper bg-blue">
            <i class="el-icon-user-solid card-icon"></i>
          </div>
          <div class="card-content">
            <div class="card-title">今日预约</div>
            <count-to :start-val="0" :end-val="todayAppointmentsCount" :duration="2000" class="card-num" />
            <div class="card-footer">
              <span>较昨日</span>
              <span :class="todayAppointmentsChangePercentage >= 0 ? 'text-success' : 'text-danger'">
                <i :class="todayAppointmentsChangePercentage >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
                {{ Math.abs(todayAppointmentsChangePercentage) }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-icon-wrapper bg-green">
            <i class="el-icon-monitor card-icon"></i>
          </div>
          <div class="card-content">
            <div class="card-title">设备总数</div>
            <count-to :start-val="0" :end-val="deviceTotal" :duration="2000" class="card-num" />
            <div class="card-footer">
              <span>在线设备</span>
              <span class="text-success">{{ deviceOnline }} 台</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-icon-wrapper bg-orange">
            <i class="el-icon-message card-icon"></i>
          </div>
          <div class="card-content">
            <div class="card-title">待处理申请</div>
            <count-to :start-val="0" :end-val="pendingAppointments" :duration="2000" class="card-num" />
            <div class="card-footer" @click="goToPage('/reservation/studyRequestList')">
              <span>点击查看</span>
              <span type="text"  >
                <i class="el-icon-right"></i>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-icon-wrapper bg-purple">
            <i class="el-icon-date card-icon"></i>
          </div>
          <div class="card-content">
            <div class="card-title">本月预约总数</div>
            <count-to :start-val="0" :end-val="monthAppointmentsCount" :duration="2000" class="card-num" />
            <div class="card-footer">
              <span>较上月</span>
              <span :class="monthAppointmentsChangePercentage >= 0 ? 'text-success' : 'text-danger'">
                <i :class="monthAppointmentsChangePercentage >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
                {{ Math.abs(monthAppointmentsChangePercentage) }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <!-- 预约趋势图 -->
      <el-col :xs="24" :lg="16">
        <el-card shadow="hover" class="chart-card ">
          <div slot="header" class="chart-header clearfix">
            <span class="chart-title">预约趋势</span>
            <div class="chart-controls">
              <el-radio-group v-model="appointmentChartType" size="mini">
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div class="chart-container">
            <appointment-trend-chart :chart-data="appointmentTrendData" :height="'300px'" />
          </div>
        </el-card>
      </el-col>

      <!-- 预约类型分布 -->
      <el-col :xs="24" :lg="8">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="chart-header clearfix">
            <span class="chart-title">预约类型分布</span>
          </div>
          <div class="chart-container">
            <appointment-type-pie :chart-data="appointmentTypeData" :height="'300px'" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备使用情况和快速入口 -->
    <el-row :gutter="20" class="chart-row">
      <!-- 设备使用情况 -->
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="chart-header clearfix">
            <span class="chart-title">设备工作量统计</span>
          </div>
          <div class="chart-container">
            <device-usage-chart :chart-data="deviceUsageData" :height="'300px'" />
          </div>
        </el-card>
      </el-col>

      <!-- 快速入口 -->
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="shortcut-card">
          <div slot="header" class="chart-header clearfix">
            <span class="chart-title">快速入口</span>
          </div>
          <div class="shortcut-container">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="shortcut-item" @click="goToPage('/reservation/studyRequestList')">
                  <i class="el-icon-date"></i>
                  <span>预约申请</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="shortcut-item" @click="goToPage('/reservation/appointmentList')">
                  <i class="el-icon-tickets"></i>
                  <span>预约管理</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="shortcut-item" @click="goToPage('/plan/devicePlan')">
                  <i class="el-icon-time"></i>
                  <span>排班管理</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="shortcut-item" @click="goToPage('/device')">
                  <i class="el-icon-monitor"></i>
                  <span>设备管理</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="shortcut-item" @click="goToPage('/rule/deviceRule')">
                  <i class="el-icon-setting"></i>
                  <span>规则管理</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="shortcut-item" @click="goToPage('/system/dict')">
                  <i class="el-icon-notebook-2"></i>
                  <span>字典管理</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CountTo from 'vue-count-to'
// 导入API
import { getIndexStatistics, getDeviceWorkload, getAppointmentTrend, getAppointmentTypeDistribution } from "@/api/index/statistics";
// 导入自定义图表组件
import AppointmentTrendChart from './dashboard/AppointmentTrendChart'
import AppointmentTypePie from './dashboard/AppointmentTypePie'
import DeviceUsageChart from './dashboard/DeviceUsageChart'

export default {
  name: "Index",
  components: {
    CountTo,
    AppointmentTrendChart,
    AppointmentTypePie,
    DeviceUsageChart
  },
  data() {
    return {
      // 顶部卡片数据
      todayAppointmentsCount: 0,
      todayAppointmentsChangePercentage: 0,
      deviceTotal: 0,
      deviceOnline: 0,
      pendingAppointments: 0,
      monthAppointmentsCount: 0,
      monthAppointmentsChangePercentage: 0,

      // 图表数据
      appointmentChartType: 'week',
      appointmentTrendData: {
        expectedData: [0, 0, 0, 0, 0, 0, 0],
        actualData: [0, 0, 0, 0, 0, 0, 0]
      },
      appointmentTypeData: [
        { value: 0, name: '普通预约' },
        { value: 0, name: '急诊预约' },
        { value: 0, name: '预留预约' }
      ],
      deviceUsageData: [
        { deviceName: '加载中...', completedCount: 0, scheduledCount: 0 }
      ]
    }
  },
  created() {
    this.getStatisticsData();
  },
  methods: {
    // 获取统计数据
    getStatisticsData() {
      this.getIndexData();
      this.getDeviceData();
      this.getAppointmentTrendData();
      this.getAppointmentTypeData();

      // 监听图表类型变化，更新趋势图数据
      this.$watch('appointmentChartType', (type) => {
        this.getAppointmentTrendData(type);
      });
    },

    // 获取首页基础统计数据
    getIndexData() {
      getIndexStatistics().then(response => {
        if (response.code === 200 && response.data) {
          const data = response.data;

          // 今日预约数据
          if (data.todayAppointments) {
            this.todayAppointmentsCount = data.todayAppointments.count || 0;
            this.todayAppointmentsChangePercentage = data.todayAppointments.changePercentage || 0;
          }

          // 本月预约数据
          if (data.monthAppointments) {
            this.monthAppointmentsCount = data.monthAppointments.count || 0;
            this.monthAppointmentsChangePercentage = data.monthAppointments.changePercentage || 0;
          }

          // 待处理申请数据
          this.pendingAppointments = data.pendingAppointments || 0;

          // 设备数据
          if (data.deviceStatistics) {
            this.deviceTotal = data.deviceStatistics.total || 0;
            this.deviceOnline = data.deviceStatistics.online || 0;
          } else {
            // 如果没有设备统计数据，设置默认值
            this.deviceTotal = 10;
            this.deviceOnline = 8;
          }
        }
      }).catch(() => {
        // 设置默认值，防止接口异常
        this.todayAppointmentsCount = 0;
        this.todayAppointmentsChangePercentage = 0;
        this.monthAppointmentsCount = 2;
        this.monthAppointmentsChangePercentage = 100;
        this.pendingAppointments = 3;
        this.deviceTotal = 10;
        this.deviceOnline = 8;
      });
    },

    // 获取设备工作量数据
    getDeviceData() {
      getDeviceWorkload().then(response => {
        if (response.code === 200 && response.data) {
          const deviceData = response.data;
          if (deviceData.length > 0) {
            this.deviceUsageData = deviceData;
          } else {
            // 如果没有数据，设置默认值
            this.deviceUsageData = [
              { deviceName: 'CT设备', completedCount: 15, scheduledCount: 20 },
              { deviceName: 'MRI设备', completedCount: 10, scheduledCount: 15 },
              { deviceName: 'DR设备', completedCount: 18, scheduledCount: 22 },
              { deviceName: '超声设备', completedCount: 12, scheduledCount: 18 },
              { deviceName: '内窥镜', completedCount: 8, scheduledCount: 12 }
            ];
          }
        }
      }).catch(() => {
        // 设置默认值，防止接口异常
        this.deviceUsageData = [
          { deviceName: 'CT设备', completedCount: 15, scheduledCount: 20 },
          { deviceName: 'MRI设备', completedCount: 10, scheduledCount: 15 },
          { deviceName: 'DR设备', completedCount: 18, scheduledCount: 22 },
          { deviceName: '超声设备', completedCount: 12, scheduledCount: 18 },
          { deviceName: '内窥镜', completedCount: 8, scheduledCount: 12 }
        ];
      });
    },

    // 获取预约趋势数据
    getAppointmentTrendData(type) {
      const chartType = type || this.appointmentChartType;

      getAppointmentTrend({ type: chartType }).then(response => {
        if (response.code === 200 && response.data) {
          const trendData = response.data;
          this.appointmentTrendData = {
            expectedData: trendData.expectedData || [30, 32, 35, 38, 40, 35, 30],
            actualData: trendData.actualData || [28, 30, 32, 36, 38, 32, 28]
          };
        }
      }).catch(() => {
        // 设置默认值，防止接口异常
        if (chartType === 'week') {
          this.appointmentTrendData = {
            expectedData: [30, 32, 35, 38, 40, 35, 30],
            actualData: [28, 30, 32, 36, 38, 32, 28]
          };
        } else {
          this.appointmentTrendData = {
            expectedData: [100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 540, 560, 580, 600, 620, 640, 660, 680].slice(0, 30),
            actualData: [90, 110, 130, 150, 170, 190, 210, 230, 250, 270, 290, 310, 330, 350, 370, 390, 410, 430, 450, 470, 490, 510, 530, 550, 570, 590, 610, 630, 650, 670].slice(0, 30)
          };
        }
      });
    },

    // 获取预约类型分布数据
    getAppointmentTypeData() {
      getAppointmentTypeDistribution().then(response => {
        if (response.code === 200 && response.data) {
          const typeData = response.data;
          this.appointmentTypeData = [
            { value: typeData.ordinary || 320, name: '普通预约' },
            { value: typeData.emergency || 120, name: '急诊预约' },
            { value: typeData.reserve || 80, name: '预留预约' }
          ];
        }
      }).catch(() => {
        // 设置默认值，防止接口异常
        this.appointmentTypeData = [
          { value: 320, name: '普通预约' },
          { value: 120, name: '急诊预约' },
          { value: 80, name: '预留预约' }
        ];
      });
    },
    // 页面跳转
    goToPage(path) {
      this.$router.push(path);
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f0f2f5;

  .data-card {
    height: 150px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    padding: 15px;

    .card-icon-wrapper {
      width: 64px;
      height: 64px;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;
      padding: 5px;
      box-sizing: border-box;

      &.bg-blue { background-color: #409EFF; }
      &.bg-green { background-color: #67C23A; }
      &.bg-orange { background-color: #E6A23C; }
      &.bg-purple { background-color: #6959CD; }

      .card-icon {
        font-size: 28px;
        color: #fff;
        line-height: 1;
        margin: 0;
        padding: 0;
      }
    }

    .card-content {
      flex: 1;

      .card-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 5px;
      }

      .card-num {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }

      .card-footer {
        font-size: 12px;
        color: #909399;

        .text-success {
          color: #67C23A;
          margin-left: 5px;
        }

        .text-danger {
          color: #F56C6C;
          margin-left: 5px;
        }
      }
    }
  }

  .chart-row {
    margin-bottom: 20px;
  }



  .chart-header {
    position: relative;
    padding-bottom: 10px;
    height: 40px; /* 固定高度 */
    display: flex;
    align-items: center;
    justify-content: space-between;


    .chart-title {
      position: relative;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      padding-left: 10px;
      line-height: 40px; /* 与高度一致 */

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: #409EFF;
        border-radius: 2px;
      }
    }

    .chart-controls {
      display: flex;
      align-items: center;
      height: 40px;
    }
  }
  .chart-card, .shortcut-card {
    margin-bottom: 20px;
    height: 380px; /* 固定高度 */
    overflow: hidden;
  }

  .chart-card .chart-container {
    height: 300px;
  }

  .shortcut-card .shortcut-container {
    height: 300px; /* 与图表容器高度一致 */
    padding: 10px 0;
    overflow-y: auto; /* 内容过多时可滚动 */
  }

  .shortcut-card .shortcut-container .shortcut-item {
    height: 90px; /* 稍微减小高度 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 15px; /* 减小底部间距 */
    transition: all 0.3s;

    i {
      font-size: 30px;
      margin-bottom: 10px;
      color: #409EFF;
    }

    span {
      font-size: 14px;
      color: #606266;
    }

    &:hover {
      background-color: #ecf5ff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }
}
</style>