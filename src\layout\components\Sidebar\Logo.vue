<template>
  <div class="sidebar-logo-container" :class="{'collapse':collapse}" :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <div class="logo-icon-container">
          <i class="el-icon-s-custom logo-icon"></i>
        </div>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <div class="logo-content">
          <div class="logo-icon-container">
            <i class="el-icon-s-custom logo-icon"></i>
          </div>
          <div class="logo-text">
            <h1 class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">{{ title }}</h1>
            <p class="sidebar-subtitle" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">Medical System</p>
          </div>
        </div>
      </router-link>
    </transition>
  </div>
</template>

<script>
import logoImg from '@/assets/logo/logo.png'
import variables from '@/assets/styles/variables.scss'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  computed: {
    variables() {
      return variables;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme
    }
  },
  data() {
    return {
      title: process.env.VUE_APP_TITLE || '智慧医疗预约',
      logo: logoImg
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  text-align: center;
  overflow: hidden;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;

    .logo-content {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
    }

    .logo-icon-container {
      width: 36px;
      height: 36px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);

      .logo-icon {
        font-size: 20px;
        color: white;
      }
    }

    .logo-text {
      text-align: left;

      .sidebar-title {
        margin: 0;
        color: #fff !important;
        font-weight: 700;
        font-size: 16px;
        font-family: 'Microsoft YaHei', 'PingFang SC', Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
        line-height: 1.2;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }

      .sidebar-subtitle {
        margin: 0;
        color: rgba(255, 255, 255, 0.8) !important;
        font-size: 11px;
        font-weight: 400;
        line-height: 1;
        margin-top: 2px;
        opacity: 0.9;
      }
    }
  }

  &.collapse {
    .logo-icon-container {
      margin-right: 0;
    }
  }

  // 悬停效果
  &:hover {
    .logo-icon-container {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.05);
      transition: all 0.3s ease;
    }
  }
}

// 深色主题适配
.sidebar-logo-container[style*="theme-dark"] {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);

  .logo-icon-container {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

// 浅色主题适配
.sidebar-logo-container[style*="menuLightBackground"] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
</style>
