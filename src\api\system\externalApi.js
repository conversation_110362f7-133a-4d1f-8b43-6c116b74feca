import request from '@/utils/request'

// 查询外部接口列表
export function listExternalApi(query) {
  return request({
    url: '/system/api/list',
    method: 'get',
    params: query
  })
}

// 查询外部接口详细
export function getExternalApi(apiId) {
  return request({
    url: '/system/api/' + apiId,
    method: 'get'
  })
}

// 新增外部接口
export function addExternalApi(data) {
  return request({
    url: '/system/api',
    method: 'post',
    data: data
  })
}

// 修改外部接口
export function updateExternalApi(data) {
  return request({
    url: '/system/api',
    method: 'put',
    data: data
  })
}

// 删除外部接口
export function delExternalApi(apiId) {
  return request({
    url: '/system/api/' + apiId,
    method: 'delete'
  })
}

// 测试外部接口连接
export function testExternalApiConnection(apiCode, data) {
  return request({
    url: '/system/api/test/' + apiCode,
    method: 'post',
    data: data
  })
}
